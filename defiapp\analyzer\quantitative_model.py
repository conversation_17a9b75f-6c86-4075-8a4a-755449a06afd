"""
量化分析模型
"""
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class QuantitativeModel:
    """量化分析模型"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.price_model = LinearRegression()
        self.volatility_model = LinearRegression()
        self.is_trained = False
    
    def calculate_technical_indicators(self, price_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """計算技術指標"""
        if not price_data:
            return {}
        
        # 轉換為 DataFrame
        df = pd.DataFrame(price_data)
        df['collected_at'] = pd.to_datetime(df['collected_at'])
        df = df.sort_values('collected_at')
        
        prices = df['price_usd'].values
        volumes = df['volume_24h'].values
        
        indicators = {}
        
        try:
            # 移動平均線
            if len(prices) >= 7:
                indicators['sma_7'] = np.mean(prices[-7:])
            if len(prices) >= 14:
                indicators['sma_14'] = np.mean(prices[-14:])
            
            # 價格變化率
            if len(prices) >= 2:
                indicators['price_change_rate'] = (prices[-1] - prices[0]) / prices[0] if prices[0] != 0 else 0
            
            # 波動率 (標準差)
            if len(prices) >= 7:
                indicators['volatility_7d'] = np.std(prices[-7:]) / np.mean(prices[-7:]) if np.mean(prices[-7:]) != 0 else 0
            
            # 交易量趨勢
            if len(volumes) >= 7:
                recent_volume = np.mean(volumes[-3:])
                historical_volume = np.mean(volumes[-7:-3]) if len(volumes) >= 7 else np.mean(volumes)
                indicators['volume_trend'] = (recent_volume - historical_volume) / historical_volume if historical_volume != 0 else 0
            
            # RSI 簡化版本
            if len(prices) >= 14:
                price_changes = np.diff(prices[-14:])
                gains = price_changes[price_changes > 0]
                losses = -price_changes[price_changes < 0]
                
                avg_gain = np.mean(gains) if len(gains) > 0 else 0
                avg_loss = np.mean(losses) if len(losses) > 0 else 0
                
                if avg_loss != 0:
                    rs = avg_gain / avg_loss
                    indicators['rsi'] = 100 - (100 / (1 + rs))
                else:
                    indicators['rsi'] = 100
        
        except Exception as e:
            logger.error(f"計算技術指標時發生錯誤: {e}")
        
        return indicators
    
    def calculate_risk_metrics(self, price_data: List[Dict[str, Any]], 
                             tvl_data: List[Dict[str, Any]] = None) -> Dict[str, float]:
        """計算風險指標"""
        risk_metrics = {}
        
        try:
            if price_data:
                df = pd.DataFrame(price_data)
                prices = df['price_usd'].values
                
                # 價格波動風險
                if len(prices) >= 7:
                    returns = np.diff(np.log(prices[prices > 0]))
                    risk_metrics['volatility_risk'] = np.std(returns) * np.sqrt(365)  # 年化波動率
                    risk_metrics['max_drawdown'] = self._calculate_max_drawdown(prices)
                
                # 流動性風險 (基於交易量)
                volumes = df['volume_24h'].values
                if len(volumes) > 0:
                    avg_volume = np.mean(volumes)
                    market_caps = df['market_cap'].values
                    avg_market_cap = np.mean(market_caps[market_caps > 0])
                    
                    if avg_market_cap > 0:
                        risk_metrics['liquidity_risk'] = 1 - min(1.0, avg_volume / (avg_market_cap * 0.01))
            
            # TVL 風險
            if tvl_data:
                tvl_df = pd.DataFrame(tvl_data)
                tvl_changes = tvl_df['change_7d'].values
                if len(tvl_changes) > 0:
                    risk_metrics['tvl_volatility'] = np.std(tvl_changes)
                    risk_metrics['tvl_trend'] = np.mean(tvl_changes)
        
        except Exception as e:
            logger.error(f"計算風險指標時發生錯誤: {e}")
        
        return risk_metrics
    
    def _calculate_max_drawdown(self, prices: np.ndarray) -> float:
        """計算最大回撤"""
        if len(prices) < 2:
            return 0.0
        
        peak = prices[0]
        max_drawdown = 0.0
        
        for price in prices[1:]:
            if price > peak:
                peak = price
            else:
                drawdown = (peak - price) / peak
                max_drawdown = max(max_drawdown, drawdown)
        
        return max_drawdown
    
    def predict_price_movement(self, features: Dict[str, float]) -> Dict[str, float]:
        """預測價格走勢"""
        prediction = {
            'direction': 'neutral',
            'confidence': 0.0,
            'expected_return_7d': 0.0,
            'risk_score': 0.5
        }
        
        try:
            # 簡化的預測邏輯
            score = 0.0
            
            # 技術指標權重
            if 'rsi' in features:
                rsi = features['rsi']
                if rsi < 30:  # 超賣
                    score += 0.3
                elif rsi > 70:  # 超買
                    score -= 0.3
            
            if 'price_change_rate' in features:
                score += features['price_change_rate'] * 0.2
            
            if 'volume_trend' in features:
                score += features['volume_trend'] * 0.1
            
            if 'volatility_7d' in features:
                # 高波動性增加風險但也可能帶來機會
                volatility = features['volatility_7d']
                prediction['risk_score'] = min(1.0, volatility * 2)
            
            # 確定方向和信心度
            if score > 0.1:
                prediction['direction'] = 'bullish'
                prediction['confidence'] = min(1.0, abs(score))
                prediction['expected_return_7d'] = score * 0.5
            elif score < -0.1:
                prediction['direction'] = 'bearish'
                prediction['confidence'] = min(1.0, abs(score))
                prediction['expected_return_7d'] = score * 0.5
            else:
                prediction['direction'] = 'neutral'
                prediction['confidence'] = 0.5
                prediction['expected_return_7d'] = 0.0
        
        except Exception as e:
            logger.error(f"預測價格走勢時發生錯誤: {e}")
        
        return prediction
    
    def calculate_portfolio_metrics(self, holdings: List[Dict[str, Any]]) -> Dict[str, float]:
        """計算投資組合指標"""
        metrics = {
            'total_value': 0.0,
            'total_return': 0.0,
            'portfolio_risk': 0.0,
            'sharpe_ratio': 0.0,
            'diversification_score': 0.0
        }
        
        try:
            if not holdings:
                return metrics
            
            total_value = sum(holding.get('current_value', 0) for holding in holdings)
            total_cost = sum(holding.get('cost_basis', 0) for holding in holdings)
            
            metrics['total_value'] = total_value
            if total_cost > 0:
                metrics['total_return'] = (total_value - total_cost) / total_cost
            
            # 計算投資組合風險 (簡化版本)
            weights = [holding.get('current_value', 0) / total_value for holding in holdings] if total_value > 0 else []
            individual_risks = [holding.get('risk_score', 0.5) for holding in holdings]
            
            if weights and individual_risks:
                metrics['portfolio_risk'] = sum(w * r for w, r in zip(weights, individual_risks))
                
                # 多樣化分數
                metrics['diversification_score'] = 1.0 - sum(w**2 for w in weights)
        
        except Exception as e:
            logger.error(f"計算投資組合指標時發生錯誤: {e}")
        
        return metrics
