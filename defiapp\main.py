"""
DeFi 自動化投研系統主程式
"""
import asyncio
import logging
from pathlib import Path

from defiapp.config import PROJECT_ROOT

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(PROJECT_ROOT / "logs" / "defiapp.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


async def main():
    """主程式入口"""
    logger.info("DeFi 自動化投研系統啟動")
    
    try:
        # 初始化資料庫
        from defiapp.database.models import init_database
        await init_database()
        
        # 啟動數據收集
        from defiapp.data_collector.collector import DataCollector
        collector = DataCollector()
        
        # 啟動分析引擎
        from defiapp.analyzer.engine import AnalysisEngine
        analyzer = AnalysisEngine()
        
        # 啟動 UI 介面
        from defiapp.ui.flask_app import create_app
        app = create_app()

        logger.info("系統初始化完成")

        # 啟動 Flask 應用
        app.run(host="0.0.0.0", port=5000, debug=False)
        
    except Exception as e:
        logger.error(f"系統啟動失敗: {e}")
        raise


if __name__ == "__main__":
    # 確保必要目錄存在
    (PROJECT_ROOT / "data").mkdir(exist_ok=True)
    (PROJECT_ROOT / "logs").mkdir(exist_ok=True)
    
    asyncio.run(main())
