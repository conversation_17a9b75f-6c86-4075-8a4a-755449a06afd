"""
數據收集器測試
"""
import unittest
from unittest.mock import Mock, patch
from defiapp.data_collector.rss_collector import <PERSON><PERSON>ollector
from defiapp.data_collector.api_collector import APICollector
from defiapp.data_collector.collector import DataCollector


class TestRSSCollector(unittest.TestCase):
    """RSS 收集器測試"""
    
    def setUp(self):
        self.collector = RSSCollector()
    
    def test_contains_keywords(self):
        """測試關鍵字檢測"""
        # 模擬新聞條目
        entry_with_keyword = {
            'title': 'DeFi Protocol Launches New Feature',
            'description': 'A major DeFi protocol has announced...'
        }
        
        entry_without_keyword = {
            'title': 'Stock Market Update',
            'description': 'Traditional markets showed...'
        }
        
        self.assertTrue(self.collector._contains_keywords(entry_with_keyword))
        self.assertFalse(self.collector._contains_keywords(entry_without_keyword))
    
    @patch('feedparser.parse')
    def test_parse_feed(self, mock_parse):
        """測試 RSS feed 解析"""
        # 模擬 feedparser 返回值
        mock_parse.return_value = Mock()
        mock_parse.return_value.entries = [
            {
                'title': 'DeFi News',
                'link': 'https://example.com/news',
                'description': 'DeFi protocol update',
                'published': 'Mon, 01 Jan 2024 12:00:00 GMT'
            }
        ]
        mock_parse.return_value.feed = {'title': 'Test Feed'}
        
        result = self.collector._parse_feed('https://example.com/rss')
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['title'], 'DeFi News')


class TestAPICollector(unittest.TestCase):
    """API 收集器測試"""
    
    def setUp(self):
        self.collector = APICollector()
    
    @patch('requests.Session.get')
    def test_collect_coingecko_data(self, mock_get):
        """測試 CoinGecko 數據收集"""
        # 模擬 API 響應
        mock_response = Mock()
        mock_response.json.return_value = {
            'ethereum': {
                'usd': 2000,
                'usd_market_cap': 240000000000,
                'usd_24h_vol': 15000000000,
                'usd_24h_change': 5.2
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.collector.collect_coingecko_data(['ethereum'])
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['coin_id'], 'ethereum')
        self.assertEqual(result[0]['price_usd'], 2000)
    
    @patch('requests.Session.get')
    def test_collect_defillama_tvl(self, mock_get):
        """測試 DefiLlama TVL 數據收集"""
        # 模擬 API 響應
        mock_response = Mock()
        mock_response.json.return_value = [
            {
                'name': 'Uniswap',
                'tvl': 5000000000,
                'change_1d': 2.5,
                'change_7d': -1.2,
                'category': 'DEX',
                'chains': ['Ethereum']
            }
        ]
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.collector.collect_defillama_tvl()
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['protocol_name'], 'Uniswap')
        self.assertEqual(result[0]['tvl'], 5000000000)


class TestDataCollector(unittest.TestCase):
    """主數據收集器測試"""
    
    def setUp(self):
        self.collector = DataCollector()
    
    @patch.object(RSSCollector, 'collect_news')
    @patch.object(APICollector, 'collect_coingecko_data')
    @patch.object(APICollector, 'collect_defillama_tvl')
    def test_collect_all_data(self, mock_tvl, mock_prices, mock_news):
        """測試完整數據收集"""
        # 設置模擬返回值
        mock_news.return_value = [{'title': 'Test News'}]
        mock_prices.return_value = [{'coin_id': 'ethereum', 'price_usd': 2000}]
        mock_tvl.return_value = [{'protocol_name': 'Uniswap', 'tvl': 5000000000}]
        
        result = self.collector.collect_all_data()
        
        self.assertIn('news', result)
        self.assertIn('prices', result)
        self.assertIn('tvl', result)
        self.assertEqual(len(result['news']), 1)
        self.assertEqual(len(result['prices']), 1)
        self.assertEqual(len(result['tvl']), 1)


if __name__ == '__main__':
    unittest.main()
