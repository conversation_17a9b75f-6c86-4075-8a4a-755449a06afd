#!/usr/bin/env python3
"""
DeFi 自動化投研系統啟動腳本
"""
import sys
import os
import asyncio
import argparse
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from defiapp.main import main


def setup_environment():
    """設置環境"""
    # 確保必要目錄存在
    (project_root / "data").mkdir(exist_ok=True)
    (project_root / "logs").mkdir(exist_ok=True)
    
    # 設置環境變數示例
    os.environ.setdefault('FLASK_ENV', 'production')
    
    print("環境設置完成")


def run_tests():
    """運行測試"""
    import unittest
    
    # 發現並運行所有測試
    loader = unittest.TestLoader()
    start_dir = project_root / 'tests'
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


def collect_data_only():
    """僅收集數據"""
    from defiapp.data_collector.collector import DataCollector
    from defiapp.database.models import DatabaseManager
    
    print("開始收集數據...")
    
    # 初始化組件
    db_manager = DatabaseManager()
    db_manager.init_database()
    
    collector = DataCollector()
    
    # 收集數據
    data = collector.collect_all_data()
    
    # 存儲數據
    news_count = db_manager.insert_news(data.get('news', []))
    price_count = db_manager.insert_price_data(data.get('prices', []))
    tvl_count = db_manager.insert_tvl_data(data.get('tvl', []))
    
    print(f"數據收集完成:")
    print(f"  新聞: {news_count} 條")
    print(f"  價格: {price_count} 個")
    print(f"  TVL: {tvl_count} 個")


def analyze_data_only():
    """僅分析數據"""
    from defiapp.database.models import DatabaseManager
    from defiapp.analyzer.engine import AnalysisEngine
    
    print("開始分析數據...")
    
    # 初始化組件
    db_manager = DatabaseManager()
    analyzer = AnalysisEngine()
    
    # 獲取未分析的新聞
    recent_news = db_manager.get_recent_news(limit=100)
    unanalyzed_news = [news for news in recent_news if not news.get('sentiment_score')]
    
    if not unanalyzed_news:
        print("沒有需要分析的新聞")
        return
    
    # 分析新聞
    analyzed_news = analyzer.analyze_news_batch(unanalyzed_news)
    
    print(f"完成 {len(analyzed_news)} 條新聞分析")
    
    # 顯示分析結果示例
    for i, news in enumerate(analyzed_news[:5], 1):
        print(f"\n{i}. {news.get('title', '')[:50]}...")
        print(f"   情緒分數: {news.get('sentiment_score', 0):.3f}")
        print(f"   事件類型: {', '.join(news.get('event_types', []))}")
        print(f"   相關項目: {', '.join(news.get('mentioned_projects', []))}")


def show_status():
    """顯示系統狀態"""
    from defiapp.database.models import DatabaseManager
    
    print("系統狀態檢查...")
    
    try:
        db_manager = DatabaseManager()
        
        # 檢查資料庫
        recent_news = db_manager.get_recent_news(limit=5)
        top_protocols = db_manager.get_top_protocols_by_tvl(limit=5)
        
        print(f"\n資料庫狀態:")
        print(f"  最近新聞: {len(recent_news)} 條")
        print(f"  協議數據: {len(top_protocols)} 個")
        
        if recent_news:
            print(f"  最新新聞時間: {recent_news[0].get('published_at', 'N/A')}")
        
        if top_protocols:
            print(f"  最大 TVL: ${top_protocols[0].get('max_tvl', 0)/1e9:.2f}B")
        
        print("\n系統組件狀態: ✅ 正常")
        
    except Exception as e:
        print(f"❌ 系統檢查失敗: {e}")


def main_cli():
    """命令行主函數"""
    parser = argparse.ArgumentParser(description='DeFi 自動化投研系統')
    parser.add_argument('command', choices=[
        'run', 'test', 'collect', 'analyze', 'status'
    ], help='執行命令')
    
    args = parser.parse_args()
    
    # 設置環境
    setup_environment()
    
    if args.command == 'run':
        print("啟動 DeFi 自動化投研系統...")
        asyncio.run(main())
    
    elif args.command == 'test':
        print("運行測試...")
        success = run_tests()
        sys.exit(0 if success else 1)
    
    elif args.command == 'collect':
        collect_data_only()
    
    elif args.command == 'analyze':
        analyze_data_only()
    
    elif args.command == 'status':
        show_status()


if __name__ == '__main__':
    main_cli()
