"""
配置文件
"""
import os
from pathlib import Path

# 嘗試載入 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # 如果沒有安裝 python-dotenv，忽略
    pass

# 專案根目錄
PROJECT_ROOT = Path(__file__).parent.parent

# 資料庫配置
DATABASE_PATH = PROJECT_ROOT / "data" / "defi_research.db"

# API 配置
COINGECKO_API_URL = "https://api.coingecko.com/api/v3"
DEFILLAMA_API_URL = "https://api.llama.fi"
CRYPTOPANIC_API_URL = "https://cryptopanic.com/api/v1"

# Twitter API 配置
# 方式1: 使用官方 Bearer Token
TWITTER_BEARER_TOKEN = os.getenv("TWITTER_BEARER_TOKEN")

# 方式2: 使用瀏覽器 Cookie 認證 (推薦)
TWITTER_COOKIE_STRING = "8c8cc75e895e36362e69a8945b13710747b3d177;ct0=c6b70a625a2b8758403cf3002619e193e5a86c3085677677d163106ced5dcb76fd8e5f76408815807837174f69b4caa99a0ad9c243bd0a7f626887080cf3388fbd1b7137c1abb0151fd9126d4ea80bd9"

# 從 cookie 字符串中提取認證信息
def _extract_twitter_auth():
    if TWITTER_COOKIE_STRING:
        import re
        auth_match = re.search(r'([a-fA-F0-9]{40})', TWITTER_COOKIE_STRING)
        ct0_match = re.search(r'ct0=([a-fA-F0-9]+)', TWITTER_COOKIE_STRING)

        if auth_match and ct0_match:
            return auth_match.group(1), ct0_match.group(1)
    return None, None

TWITTER_AUTH_TOKEN, TWITTER_CT0 = _extract_twitter_auth()

# Telegram Bot 配置
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# LINE Notify 配置
LINE_NOTIFY_TOKEN = os.getenv("LINE_NOTIFY_TOKEN")

# 分析參數
INVESTMENT_SCORE_WEIGHTS = {
    "hype_score": 0.3,
    "tvl_growth": 0.2,
    "sentiment_score": 0.2,
    "unlock_risk": -0.3
}

# RSS 來源
RSS_FEEDS = [
    "https://cointelegraph.com/rss",
    "https://www.coindesk.com/arc/outboundfeeds/rss/",
    "https://theblock.co/rss.xml"
]

# 監控的項目關鍵字
MONITORED_KEYWORDS = [
    "DeFi", "yield farming", "liquidity mining", "staking",
    "DEX", "AMM", "lending", "borrowing", "governance token"
]

# Twitter 配置
TWITTER_RATE_LIMIT_WINDOW = 15 * 60  # 15分鐘窗口
TWITTER_MAX_REQUESTS_PER_WINDOW = 300  # 每窗口最大請求數
TWITTER_COLLECTION_INTERVAL = 30 * 60  # 30分鐘收集一次

# Twitter 重要帳號列表 (可以根據需要調整)
TWITTER_VIP_ACCOUNTS = [
    'uniswap', 'aaveaave', 'compoundfinance', 'makerdao',
    'curvefinance', 'yearnfinance', 'sushiswap', 'pancakeswap',
    'defillama', 'cryptorank_io', 'haydenzadams', 'stani'
]

# Twitter 分析權重
TWITTER_ANALYSIS_WEIGHTS = {
    'sentiment_weight': 0.4,
    'influence_weight': 0.3,
    'engagement_weight': 0.2,
    'virality_weight': 0.1
}
