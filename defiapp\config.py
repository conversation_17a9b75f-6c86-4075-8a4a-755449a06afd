"""
配置文件
"""
import os
from pathlib import Path

# 專案根目錄
PROJECT_ROOT = Path(__file__).parent.parent

# 資料庫配置
DATABASE_PATH = PROJECT_ROOT / "data" / "defi_research.db"

# API 配置
COINGECKO_API_URL = "https://api.coingecko.com/api/v3"
DEFILLAMA_API_URL = "https://api.llama.fi"
CRYPTOPANIC_API_URL = "https://cryptopanic.com/api/v1"

# Twitter API 配置 (需要設置環境變數)
TWITTER_BEARER_TOKEN = os.getenv("TWITTER_BEARER_TOKEN")

# Telegram Bot 配置
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# LINE Notify 配置
LINE_NOTIFY_TOKEN = os.getenv("LINE_NOTIFY_TOKEN")

# 分析參數
INVESTMENT_SCORE_WEIGHTS = {
    "hype_score": 0.3,
    "tvl_growth": 0.2,
    "sentiment_score": 0.2,
    "unlock_risk": -0.3
}

# RSS 來源
RSS_FEEDS = [
    "https://cointelegraph.com/rss",
    "https://www.coindesk.com/arc/outboundfeeds/rss/",
    "https://theblock.co/rss.xml"
]

# 監控的項目關鍵字
MONITORED_KEYWORDS = [
    "DeFi", "yield farming", "liquidity mining", "staking",
    "DEX", "AMM", "lending", "borrowing", "governance token"
]
