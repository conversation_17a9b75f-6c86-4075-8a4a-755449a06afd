"""
資料庫模型
"""
import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from defiapp.config import DATABASE_PATH

logger = logging.getLogger(__name__)


class DatabaseManager:
    """資料庫管理器"""
    
    def __init__(self, db_path: Path = DATABASE_PATH):
        self.db_path = db_path
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def get_connection(self) -> sqlite3.Connection:
        """獲取資料庫連接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """初始化資料庫表"""
        with self.get_connection() as conn:
            # 新聞表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS news (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    link TEXT UNIQUE,
                    description TEXT,
                    content TEXT,
                    published_at DATETIME,
                    source TEXT,
                    source_name TEXT,
                    tags TEXT,
                    sentiment_score REAL,
                    news_type TEXT,
                    tokens_mentioned TEXT,
                    collected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    processed BOOLEAN DEFAULT FALSE
                )
            ''')
            
            # 價格數據表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS price_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    coin_id TEXT NOT NULL,
                    price_usd REAL,
                    market_cap REAL,
                    volume_24h REAL,
                    change_24h REAL,
                    source TEXT,
                    collected_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # TVL 數據表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS tvl_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    protocol_name TEXT NOT NULL,
                    tvl REAL,
                    change_1d REAL,
                    change_7d REAL,
                    category TEXT,
                    chains TEXT,
                    source TEXT,
                    collected_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Twitter 數據表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS twitter_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    tweet_id TEXT UNIQUE NOT NULL,
                    text TEXT NOT NULL,
                    author_username TEXT,
                    author_display_name TEXT,
                    author_verified BOOLEAN,
                    author_followers_count INTEGER,
                    like_count INTEGER,
                    retweet_count INTEGER,
                    reply_count INTEGER,
                    quote_count INTEGER,
                    engagement_score REAL,
                    influence_score REAL,
                    sentiment_score REAL,
                    weighted_sentiment REAL,
                    hashtags TEXT,
                    mentions TEXT,
                    cashtags TEXT,
                    language TEXT,
                    created_at DATETIME,
                    collected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    processed BOOLEAN DEFAULT FALSE
                )
            ''')

            # 分析結果表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_name TEXT NOT NULL,
                    investment_score REAL,
                    hype_score REAL,
                    tvl_growth REAL,
                    sentiment_score REAL,
                    unlock_risk REAL,
                    predicted_return_7d REAL,
                    risk_level TEXT,
                    recommendation TEXT,
                    analysis_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 創建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_news_published ON news(published_at)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_news_source ON news(source)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_price_coin ON price_data(coin_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_tvl_protocol ON tvl_data(protocol_name)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_twitter_created ON twitter_data(created_at)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_twitter_author ON twitter_data(author_username)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_twitter_engagement ON twitter_data(engagement_score)')
            
            conn.commit()
            logger.info("資料庫初始化完成")
    
    def insert_news(self, news_items: List[Dict[str, Any]]) -> int:
        """插入新聞數據"""
        inserted_count = 0
        
        with self.get_connection() as conn:
            for item in news_items:
                try:
                    cursor = conn.execute('''
                        INSERT OR IGNORE INTO news
                        (title, link, description, content, published_at, source,
                         source_name, tags, collected_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        item.get('title', ''),
                        item.get('link', ''),
                        item.get('description', ''),
                        item.get('content', ''),
                        item.get('published'),
                        item.get('source', ''),
                        item.get('source_name', ''),
                        ','.join(item.get('tags', [])),
                        item.get('collected_at', datetime.now())
                    ))
                    if cursor.lastrowid:
                        inserted_count += 1
                except Exception as e:
                    logger.error(f"插入新聞數據時發生錯誤: {e}")
            
            conn.commit()
        
        logger.info(f"成功插入 {inserted_count} 條新聞")
        return inserted_count
    
    def insert_price_data(self, price_items: List[Dict[str, Any]]) -> int:
        """插入價格數據"""
        inserted_count = 0
        
        with self.get_connection() as conn:
            for item in price_items:
                try:
                    conn.execute('''
                        INSERT INTO price_data 
                        (coin_id, price_usd, market_cap, volume_24h, change_24h, 
                         source, collected_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        item.get('coin_id', ''),
                        item.get('price_usd', 0),
                        item.get('market_cap', 0),
                        item.get('volume_24h', 0),
                        item.get('change_24h', 0),
                        item.get('source', ''),
                        item.get('collected_at', datetime.now())
                    ))
                    inserted_count += 1
                except Exception as e:
                    logger.error(f"插入價格數據時發生錯誤: {e}")
            
            conn.commit()
        
        logger.info(f"成功插入 {inserted_count} 條價格數據")
        return inserted_count
    
    def insert_tvl_data(self, tvl_items: List[Dict[str, Any]]) -> int:
        """插入 TVL 數據"""
        inserted_count = 0
        
        with self.get_connection() as conn:
            for item in tvl_items:
                try:
                    conn.execute('''
                        INSERT INTO tvl_data 
                        (protocol_name, tvl, change_1d, change_7d, category, 
                         chains, source, collected_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        item.get('protocol_name', ''),
                        item.get('tvl', 0),
                        item.get('change_1d', 0),
                        item.get('change_7d', 0),
                        item.get('category', ''),
                        ','.join(item.get('chains', [])),
                        item.get('source', ''),
                        item.get('collected_at', datetime.now())
                    ))
                    inserted_count += 1
                except Exception as e:
                    logger.error(f"插入 TVL 數據時發生錯誤: {e}")
            
            conn.commit()
        
        logger.info(f"成功插入 {inserted_count} 條 TVL 數據")
        return inserted_count

    def insert_twitter_data(self, twitter_items: List[Dict[str, Any]]) -> int:
        """插入 Twitter 數據"""
        inserted_count = 0

        with self.get_connection() as conn:
            for item in twitter_items:
                try:
                    author = item.get('author', {})
                    metrics = item.get('metrics', {})
                    analysis = item.get('analysis', {})

                    cursor = conn.execute('''
                        INSERT OR IGNORE INTO twitter_data
                        (tweet_id, text, author_username, author_display_name,
                         author_verified, author_followers_count, like_count,
                         retweet_count, reply_count, quote_count, engagement_score,
                         influence_score, sentiment_score, weighted_sentiment,
                         hashtags, mentions, cashtags, language, created_at, collected_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        item.get('tweet_id', ''),
                        item.get('text', ''),
                        author.get('username', ''),
                        author.get('display_name', ''),
                        author.get('verified', False),
                        author.get('followers_count', 0),
                        metrics.get('like_count', 0),
                        metrics.get('retweet_count', 0),
                        metrics.get('reply_count', 0),
                        metrics.get('quote_count', 0),
                        item.get('engagement_score', 0),
                        analysis.get('influence_score', 0),
                        analysis.get('sentiment', {}).get('score', 0),
                        analysis.get('weighted_sentiment', 0),
                        ','.join(analysis.get('hashtags', [])),
                        ','.join(analysis.get('mentions', [])),
                        ','.join(analysis.get('cashtags', [])),
                        item.get('language', ''),
                        item.get('created_at'),
                        item.get('collected_at', datetime.now())
                    ))
                    if cursor.lastrowid:
                        inserted_count += 1
                except Exception as e:
                    logger.error(f"插入 Twitter 數據時發生錯誤: {e}")

            conn.commit()

        logger.info(f"成功插入 {inserted_count} 條 Twitter 數據")
        return inserted_count


    def get_recent_news(self, limit: int = 50) -> List[Dict[str, Any]]:
        """獲取最近的新聞"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM news
                ORDER BY published_at DESC
                LIMIT ?
            ''', (limit,))

            return [dict(row) for row in cursor.fetchall()]

    def get_price_trends(self, coin_id: str, days: int = 7) -> List[Dict[str, Any]]:
        """獲取價格趨勢"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM price_data
                WHERE coin_id = ?
                AND collected_at >= datetime('now', '-{} days')
                ORDER BY collected_at DESC
            '''.format(days), (coin_id,))

            return [dict(row) for row in cursor.fetchall()]

    def get_top_protocols_by_tvl(self, limit: int = 20) -> List[Dict[str, Any]]:
        """獲取 TVL 最高的協議"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT protocol_name, MAX(tvl) as max_tvl,
                       AVG(change_7d) as avg_change_7d,
                       category
                FROM tvl_data
                GROUP BY protocol_name
                ORDER BY max_tvl DESC
                LIMIT ?
            ''', (limit,))

            return [dict(row) for row in cursor.fetchall()]

    def get_recent_tweets(self, limit: int = 50) -> List[Dict[str, Any]]:
        """獲取最近的推文"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM twitter_data
                ORDER BY created_at DESC
                LIMIT ?
            ''', (limit,))

            return [dict(row) for row in cursor.fetchall()]

    def get_high_engagement_tweets(self, limit: int = 20) -> List[Dict[str, Any]]:
        """獲取高互動推文"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM twitter_data
                WHERE engagement_score > 100
                ORDER BY engagement_score DESC
                LIMIT ?
            ''', (limit,))

            return [dict(row) for row in cursor.fetchall()]

    def get_twitter_sentiment_summary(self, days: int = 1) -> Dict[str, Any]:
        """獲取 Twitter 情緒摘要"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT
                    COUNT(*) as total_tweets,
                    AVG(sentiment_score) as avg_sentiment,
                    AVG(weighted_sentiment) as avg_weighted_sentiment,
                    AVG(engagement_score) as avg_engagement,
                    SUM(CASE WHEN sentiment_score > 0.1 THEN 1 ELSE 0 END) as positive_count,
                    SUM(CASE WHEN sentiment_score < -0.1 THEN 1 ELSE 0 END) as negative_count
                FROM twitter_data
                WHERE created_at >= datetime('now', '-{} days')
            '''.format(days))

            result = cursor.fetchone()
            if result:
                return dict(result)
            return {}


async def init_database():
    """異步初始化資料庫"""
    db_manager = DatabaseManager()
    db_manager.init_database()
    return db_manager
