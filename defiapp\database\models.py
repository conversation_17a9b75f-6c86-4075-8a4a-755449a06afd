"""
資料庫模型
"""
import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from defiapp.config import DATABASE_PATH

logger = logging.getLogger(__name__)


class DatabaseManager:
    """資料庫管理器"""
    
    def __init__(self, db_path: Path = DATABASE_PATH):
        self.db_path = db_path
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def get_connection(self) -> sqlite3.Connection:
        """獲取資料庫連接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """初始化資料庫表"""
        with self.get_connection() as conn:
            # 新聞表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS news (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    link TEXT UNIQUE,
                    description TEXT,
                    content TEXT,
                    published_at DATETIME,
                    source TEXT,
                    source_name TEXT,
                    tags TEXT,
                    sentiment_score REAL,
                    news_type TEXT,
                    tokens_mentioned TEXT,
                    collected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    processed BOOLEAN DEFAULT FALSE
                )
            ''')
            
            # 價格數據表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS price_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    coin_id TEXT NOT NULL,
                    price_usd REAL,
                    market_cap REAL,
                    volume_24h REAL,
                    change_24h REAL,
                    source TEXT,
                    collected_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # TVL 數據表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS tvl_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    protocol_name TEXT NOT NULL,
                    tvl REAL,
                    change_1d REAL,
                    change_7d REAL,
                    category TEXT,
                    chains TEXT,
                    source TEXT,
                    collected_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 分析結果表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_name TEXT NOT NULL,
                    investment_score REAL,
                    hype_score REAL,
                    tvl_growth REAL,
                    sentiment_score REAL,
                    unlock_risk REAL,
                    predicted_return_7d REAL,
                    risk_level TEXT,
                    recommendation TEXT,
                    analysis_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 創建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_news_published ON news(published_at)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_news_source ON news(source)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_price_coin ON price_data(coin_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_tvl_protocol ON tvl_data(protocol_name)')
            
            conn.commit()
            logger.info("資料庫初始化完成")
    
    def insert_news(self, news_items: List[Dict[str, Any]]) -> int:
        """插入新聞數據"""
        inserted_count = 0
        
        with self.get_connection() as conn:
            for item in news_items:
                try:
                    cursor = conn.execute('''
                        INSERT OR IGNORE INTO news
                        (title, link, description, content, published_at, source,
                         source_name, tags, collected_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        item.get('title', ''),
                        item.get('link', ''),
                        item.get('description', ''),
                        item.get('content', ''),
                        item.get('published'),
                        item.get('source', ''),
                        item.get('source_name', ''),
                        ','.join(item.get('tags', [])),
                        item.get('collected_at', datetime.now())
                    ))
                    if cursor.lastrowid:
                        inserted_count += 1
                except Exception as e:
                    logger.error(f"插入新聞數據時發生錯誤: {e}")
            
            conn.commit()
        
        logger.info(f"成功插入 {inserted_count} 條新聞")
        return inserted_count
    
    def insert_price_data(self, price_items: List[Dict[str, Any]]) -> int:
        """插入價格數據"""
        inserted_count = 0
        
        with self.get_connection() as conn:
            for item in price_items:
                try:
                    conn.execute('''
                        INSERT INTO price_data 
                        (coin_id, price_usd, market_cap, volume_24h, change_24h, 
                         source, collected_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        item.get('coin_id', ''),
                        item.get('price_usd', 0),
                        item.get('market_cap', 0),
                        item.get('volume_24h', 0),
                        item.get('change_24h', 0),
                        item.get('source', ''),
                        item.get('collected_at', datetime.now())
                    ))
                    inserted_count += 1
                except Exception as e:
                    logger.error(f"插入價格數據時發生錯誤: {e}")
            
            conn.commit()
        
        logger.info(f"成功插入 {inserted_count} 條價格數據")
        return inserted_count
    
    def insert_tvl_data(self, tvl_items: List[Dict[str, Any]]) -> int:
        """插入 TVL 數據"""
        inserted_count = 0
        
        with self.get_connection() as conn:
            for item in tvl_items:
                try:
                    conn.execute('''
                        INSERT INTO tvl_data 
                        (protocol_name, tvl, change_1d, change_7d, category, 
                         chains, source, collected_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        item.get('protocol_name', ''),
                        item.get('tvl', 0),
                        item.get('change_1d', 0),
                        item.get('change_7d', 0),
                        item.get('category', ''),
                        ','.join(item.get('chains', [])),
                        item.get('source', ''),
                        item.get('collected_at', datetime.now())
                    ))
                    inserted_count += 1
                except Exception as e:
                    logger.error(f"插入 TVL 數據時發生錯誤: {e}")
            
            conn.commit()
        
        logger.info(f"成功插入 {inserted_count} 條 TVL 數據")
        return inserted_count


    def get_recent_news(self, limit: int = 50) -> List[Dict[str, Any]]:
        """獲取最近的新聞"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM news
                ORDER BY published_at DESC
                LIMIT ?
            ''', (limit,))

            return [dict(row) for row in cursor.fetchall()]

    def get_price_trends(self, coin_id: str, days: int = 7) -> List[Dict[str, Any]]:
        """獲取價格趨勢"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM price_data
                WHERE coin_id = ?
                AND collected_at >= datetime('now', '-{} days')
                ORDER BY collected_at DESC
            '''.format(days), (coin_id,))

            return [dict(row) for row in cursor.fetchall()]

    def get_top_protocols_by_tvl(self, limit: int = 20) -> List[Dict[str, Any]]:
        """獲取 TVL 最高的協議"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT protocol_name, MAX(tvl) as max_tvl,
                       AVG(change_7d) as avg_change_7d,
                       category
                FROM tvl_data
                GROUP BY protocol_name
                ORDER BY max_tvl DESC
                LIMIT ?
            ''', (limit,))

            return [dict(row) for row in cursor.fetchall()]


async def init_database():
    """異步初始化資料庫"""
    db_manager = DatabaseManager()
    db_manager.init_database()
    return db_manager
