"""
NLP 分析器
"""
import re
import logging
from typing import Dict, List, Any, Tuple
from collections import Counter

logger = logging.getLogger(__name__)


class NLPAnalyzer:
    """NLP 分析器"""
    
    def __init__(self):
        # 正面情緒詞彙
        self.positive_words = {
            'bullish', 'moon', 'pump', 'surge', 'rally', 'breakout', 'gains',
            'profit', 'growth', 'rise', 'increase', 'up', 'high', 'strong',
            'buy', 'long', 'invest', 'opportunity', 'potential', 'promising',
            'adoption', 'partnership', 'launch', 'upgrade', 'innovation'
        }
        
        # 負面情緒詞彙
        self.negative_words = {
            'bearish', 'dump', 'crash', 'fall', 'drop', 'decline', 'loss',
            'sell', 'short', 'risk', 'danger', 'concern', 'worry', 'fear',
            'down', 'low', 'weak', 'bad', 'negative', 'problem', 'issue',
            'hack', 'exploit', 'vulnerability', 'scam', 'fraud', 'rug'
        }
        
        # 事件類型關鍵字
        self.event_keywords = {
            'listing': ['list', 'listing', 'exchange', 'binance', 'coinbase', 'okx'],
            'investment': ['invest', 'funding', 'raise', 'round', 'vc', 'fund'],
            'tokenomics': ['unlock', 'vesting', 'burn', 'mint', 'supply', 'emission'],
            'partnership': ['partner', 'collaboration', 'integrate', 'alliance'],
            'upgrade': ['upgrade', 'update', 'fork', 'migration', 'v2', 'v3'],
            'governance': ['vote', 'proposal', 'governance', 'dao', 'community']
        }
        
        # DeFi 項目關鍵字
        self.defi_projects = {
            'uniswap', 'aave', 'compound', 'maker', 'curve', 'yearn',
            'sushi', 'pancakeswap', '1inch', 'balancer', 'synthetix',
            'chainlink', 'the graph', 'polygon', 'avalanche', 'solana'
        }
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """分析文本情緒"""
        if not text:
            return {'score': 0.0, 'label': 'neutral', 'confidence': 0.0}
        
        text_lower = text.lower()
        words = re.findall(r'\b\w+\b', text_lower)
        
        positive_count = sum(1 for word in words if word in self.positive_words)
        negative_count = sum(1 for word in words if word in self.negative_words)
        
        total_sentiment_words = positive_count + negative_count
        
        if total_sentiment_words == 0:
            return {'score': 0.0, 'label': 'neutral', 'confidence': 0.0}
        
        # 計算情緒分數 (-1 到 1)
        score = (positive_count - negative_count) / len(words) * 10
        score = max(-1.0, min(1.0, score))  # 限制在 -1 到 1 之間
        
        # 確定標籤
        if score > 0.1:
            label = 'positive'
        elif score < -0.1:
            label = 'negative'
        else:
            label = 'neutral'
        
        # 計算信心度
        confidence = min(1.0, total_sentiment_words / len(words) * 5)
        
        return {
            'score': score,
            'label': label,
            'confidence': confidence,
            'positive_words': positive_count,
            'negative_words': negative_count
        }
    
    def extract_event_type(self, text: str) -> List[str]:
        """提取事件類型"""
        if not text:
            return []
        
        text_lower = text.lower()
        detected_events = []
        
        for event_type, keywords in self.event_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                detected_events.append(event_type)
        
        return detected_events
    
    def extract_mentioned_projects(self, text: str) -> List[str]:
        """提取提到的 DeFi 項目"""
        if not text:
            return []
        
        text_lower = text.lower()
        mentioned_projects = []
        
        for project in self.defi_projects:
            if project in text_lower:
                mentioned_projects.append(project)
        
        return mentioned_projects
    
    def calculate_hype_score(self, text: str) -> float:
        """計算炒作分數"""
        if not text:
            return 0.0
        
        text_lower = text.lower()
        
        # 炒作相關詞彙
        hype_words = {
            'moon', 'rocket', 'gem', 'diamond', 'hands', 'hodl',
            'ape', 'fomo', 'pump', 'lambo', 'millionaire', 'rich'
        }
        
        # 計算炒作詞彙出現次數
        words = re.findall(r'\b\w+\b', text_lower)
        hype_count = sum(1 for word in words if word in hype_words)
        
        # 計算大寫字母比例（通常表示興奮）
        if len(text) > 0:
            caps_ratio = sum(1 for c in text if c.isupper()) / len(text)
        else:
            caps_ratio = 0
        
        # 計算感嘆號數量
        exclamation_count = text.count('!')
        
        # 綜合計算炒作分數
        hype_score = (
            hype_count * 0.4 +
            caps_ratio * 0.3 +
            min(exclamation_count, 5) * 0.3
        )
        
        return min(1.0, hype_score)  # 限制在 0-1 之間
    
    def analyze_text(self, text: str) -> Dict[str, Any]:
        """綜合分析文本"""
        sentiment = self.analyze_sentiment(text)
        event_types = self.extract_event_type(text)
        mentioned_projects = self.extract_mentioned_projects(text)
        hype_score = self.calculate_hype_score(text)
        
        return {
            'sentiment': sentiment,
            'event_types': event_types,
            'mentioned_projects': mentioned_projects,
            'hype_score': hype_score,
            'text_length': len(text),
            'word_count': len(re.findall(r'\b\w+\b', text.lower()))
        }
