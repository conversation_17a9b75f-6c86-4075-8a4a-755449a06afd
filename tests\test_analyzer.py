"""
分析器測試
"""
import unittest
from defiapp.analyzer.nlp_analyzer import NLPAnalyzer
from defiapp.analyzer.engine import AnalysisEngine
from defiapp.analyzer.quantitative_model import QuantitativeModel


class TestNLPAnalyzer(unittest.TestCase):
    """NLP 分析器測試"""
    
    def setUp(self):
        self.analyzer = NLPAnalyzer()
    
    def test_analyze_sentiment_positive(self):
        """測試正面情緒分析"""
        text = "This DeFi project is bullish and has great potential for growth"
        result = self.analyzer.analyze_sentiment(text)
        
        self.assertGreater(result['score'], 0)
        self.assertEqual(result['label'], 'positive')
        self.assertGreater(result['confidence'], 0)
    
    def test_analyze_sentiment_negative(self):
        """測試負面情緒分析"""
        text = "This project is bearish and risky, might crash soon"
        result = self.analyzer.analyze_sentiment(text)
        
        self.assertLess(result['score'], 0)
        self.assertEqual(result['label'], 'negative')
        self.assertGreater(result['confidence'], 0)
    
    def test_analyze_sentiment_neutral(self):
        """測試中性情緒分析"""
        text = "This is a regular update about the project status"
        result = self.analyzer.analyze_sentiment(text)
        
        self.assertEqual(result['label'], 'neutral')
    
    def test_extract_event_type(self):
        """測試事件類型提取"""
        text = "Binance announces new listing for DeFi token"
        result = self.analyzer.extract_event_type(text)
        
        self.assertIn('listing', result)
    
    def test_extract_mentioned_projects(self):
        """測試項目提及提取"""
        text = "Uniswap and Aave are leading DeFi protocols"
        result = self.analyzer.extract_mentioned_projects(text)
        
        self.assertIn('uniswap', result)
        self.assertIn('aave', result)
    
    def test_calculate_hype_score(self):
        """測試炒作分數計算"""
        hype_text = "TO THE MOON!!! This gem will make us rich!!!"
        normal_text = "Regular project update with technical details"
        
        hype_score = self.analyzer.calculate_hype_score(hype_text)
        normal_score = self.analyzer.calculate_hype_score(normal_text)
        
        self.assertGreater(hype_score, normal_score)
    
    def test_analyze_text_comprehensive(self):
        """測試綜合文本分析"""
        text = "Uniswap announces bullish partnership! Great potential for moon!"
        result = self.analyzer.analyze_text(text)
        
        self.assertIn('sentiment', result)
        self.assertIn('event_types', result)
        self.assertIn('mentioned_projects', result)
        self.assertIn('hype_score', result)
        
        self.assertGreater(result['sentiment']['score'], 0)
        self.assertIn('uniswap', result['mentioned_projects'])
        self.assertGreater(result['hype_score'], 0)


class TestAnalysisEngine(unittest.TestCase):
    """分析引擎測試"""
    
    def setUp(self):
        self.engine = AnalysisEngine()
    
    def test_analyze_single_news(self):
        """測試單條新聞分析"""
        news_item = {
            'title': 'DeFi Protocol Launches New Feature',
            'description': 'This bullish development shows great potential',
            'link': 'https://example.com/news'
        }
        
        result = self.engine.analyze_single_news(news_item)
        
        self.assertIn('sentiment_score', result)
        self.assertIn('sentiment_label', result)
        self.assertIn('event_types', result)
        self.assertIn('mentioned_projects', result)
        self.assertTrue(result['analysis_completed'])
    
    def test_calculate_project_scores(self):
        """測試項目評分計算"""
        project_data = {
            'project_name': 'Test Project',
            'hype_score': 0.7,
            'tvl_growth': 0.5,
            'sentiment_score': 0.3,
            'unlock_risk': 0.1
        }
        
        result = self.engine.calculate_project_scores(project_data)
        
        self.assertIn('investment_score', result)
        self.assertIn('risk_level', result)
        self.assertIn('recommendation', result)
        self.assertIn('predicted_return_7d', result)
        self.assertEqual(result['project_name'], 'Test Project')
    
    def test_generate_market_summary(self):
        """測試市場摘要生成"""
        market_data = {
            'tvl': [
                {'protocol_name': 'Uniswap', 'tvl': 5000000000, 'change_7d': 5.2},
                {'protocol_name': 'Aave', 'tvl': 3000000000, 'change_7d': -2.1}
            ],
            'prices': [
                {'coin_id': 'ethereum', 'change_24h': 3.5},
                {'coin_id': 'uniswap', 'change_24h': -1.2}
            ],
            'news': [
                {'sentiment_score': 0.5, 'mentioned_projects': ['uniswap']},
                {'sentiment_score': -0.2, 'mentioned_projects': ['aave']}
            ]
        }
        
        result = self.engine.generate_market_summary(market_data)
        
        self.assertIn('total_protocols', result)
        self.assertIn('total_tvl', result)
        self.assertIn('avg_sentiment', result)
        self.assertIn('top_gainers', result)
        self.assertIn('top_losers', result)
        self.assertEqual(result['total_protocols'], 2)


class TestQuantitativeModel(unittest.TestCase):
    """量化模型測試"""
    
    def setUp(self):
        self.model = QuantitativeModel()
    
    def test_calculate_technical_indicators(self):
        """測試技術指標計算"""
        price_data = [
            {'price_usd': 100, 'volume_24h': 1000000, 'collected_at': '2024-01-01'},
            {'price_usd': 105, 'volume_24h': 1200000, 'collected_at': '2024-01-02'},
            {'price_usd': 102, 'volume_24h': 1100000, 'collected_at': '2024-01-03'},
            {'price_usd': 108, 'volume_24h': 1300000, 'collected_at': '2024-01-04'},
            {'price_usd': 110, 'volume_24h': 1400000, 'collected_at': '2024-01-05'},
            {'price_usd': 107, 'volume_24h': 1250000, 'collected_at': '2024-01-06'},
            {'price_usd': 112, 'volume_24h': 1500000, 'collected_at': '2024-01-07'}
        ]
        
        result = self.model.calculate_technical_indicators(price_data)
        
        self.assertIn('sma_7', result)
        self.assertIn('price_change_rate', result)
        self.assertIn('volatility_7d', result)
        self.assertGreater(result['price_change_rate'], 0)  # 價格上漲
    
    def test_calculate_risk_metrics(self):
        """測試風險指標計算"""
        price_data = [
            {'price_usd': 100, 'volume_24h': 1000000, 'market_cap': 1000000000},
            {'price_usd': 95, 'volume_24h': 1200000, 'market_cap': 950000000},
            {'price_usd': 90, 'volume_24h': 1100000, 'market_cap': 900000000},
            {'price_usd': 105, 'volume_24h': 1300000, 'market_cap': 1050000000},
            {'price_usd': 110, 'volume_24h': 1400000, 'market_cap': 1100000000},
            {'price_usd': 85, 'volume_24h': 1250000, 'market_cap': 850000000},
            {'price_usd': 115, 'volume_24h': 1500000, 'market_cap': 1150000000}
        ]
        
        result = self.model.calculate_risk_metrics(price_data)
        
        self.assertIn('volatility_risk', result)
        self.assertIn('max_drawdown', result)
        self.assertIn('liquidity_risk', result)
        self.assertGreater(result['max_drawdown'], 0)  # 應該有回撤
    
    def test_predict_price_movement(self):
        """測試價格走勢預測"""
        features = {
            'rsi': 25,  # 超賣
            'price_change_rate': 0.05,  # 上漲5%
            'volume_trend': 0.1,  # 交易量增加
            'volatility_7d': 0.2
        }
        
        result = self.model.predict_price_movement(features)
        
        self.assertIn('direction', result)
        self.assertIn('confidence', result)
        self.assertIn('expected_return_7d', result)
        self.assertIn('risk_score', result)
        
        # RSI 超賣應該預測上漲
        self.assertEqual(result['direction'], 'bullish')
    
    def test_calculate_portfolio_metrics(self):
        """測試投資組合指標計算"""
        holdings = [
            {'current_value': 10000, 'cost_basis': 8000, 'risk_score': 0.3},
            {'current_value': 5000, 'cost_basis': 6000, 'risk_score': 0.5},
            {'current_value': 3000, 'cost_basis': 2500, 'risk_score': 0.2}
        ]
        
        result = self.model.calculate_portfolio_metrics(holdings)
        
        self.assertIn('total_value', result)
        self.assertIn('total_return', result)
        self.assertIn('portfolio_risk', result)
        self.assertIn('diversification_score', result)
        
        self.assertEqual(result['total_value'], 18000)
        self.assertGreater(result['total_return'], 0)  # 整體有盈利


if __name__ == '__main__':
    unittest.main()
