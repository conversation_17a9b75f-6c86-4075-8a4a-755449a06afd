"""
API 數據收集器
"""
import logging
import requests
from datetime import datetime
from typing import List, Dict, Any, Optional
from defiapp.config import (
    COINGECKO_API_URL, 
    DEFILLAMA_API_URL, 
    CRYPTOPANIC_API_URL
)

logger = logging.getLogger(__name__)


class APICollector:
    """API 數據收集器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DeFi Research Bot 1.0'
        })
    
    def collect_coingecko_data(self, coin_ids: List[str]) -> List[Dict[str, Any]]:
        """收集 CoinGecko 價格和市場數據"""
        data = []
        
        try:
            # 獲取價格數據
            url = f"{COINGECKO_API_URL}/simple/price"
            params = {
                'ids': ','.join(coin_ids),
                'vs_currencies': 'usd',
                'include_market_cap': 'true',
                'include_24hr_vol': 'true',
                'include_24hr_change': 'true'
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            price_data = response.json()
            
            for coin_id, coin_data in price_data.items():
                data.append({
                    'coin_id': coin_id,
                    'price_usd': coin_data.get('usd', 0),
                    'market_cap': coin_data.get('usd_market_cap', 0),
                    'volume_24h': coin_data.get('usd_24h_vol', 0),
                    'change_24h': coin_data.get('usd_24h_change', 0),
                    'source': 'coingecko',
                    'collected_at': datetime.now()
                })
            
            logger.info(f"從 CoinGecko 收集到 {len(data)} 個幣種數據")
            
        except Exception as e:
            logger.error(f"收集 CoinGecko 數據時發生錯誤: {e}")
        
        return data
    
    def collect_defillama_tvl(self) -> List[Dict[str, Any]]:
        """收集 DefiLlama TVL 數據"""
        data = []
        
        try:
            url = f"{DEFILLAMA_API_URL}/protocols"
            response = self.session.get(url)
            response.raise_for_status()
            
            protocols = response.json()
            
            for protocol in protocols[:50]:  # 只取前50個協議
                data.append({
                    'protocol_name': protocol.get('name', ''),
                    'tvl': protocol.get('tvl', 0),
                    'change_1d': protocol.get('change_1d', 0),
                    'change_7d': protocol.get('change_7d', 0),
                    'category': protocol.get('category', ''),
                    'chains': protocol.get('chains', []),
                    'source': 'defillama',
                    'collected_at': datetime.now()
                })
            
            logger.info(f"從 DefiLlama 收集到 {len(data)} 個協議 TVL 數據")
            
        except Exception as e:
            logger.error(f"收集 DefiLlama 數據時發生錯誤: {e}")
        
        return data
    
    def collect_crypto_news(self, auth_token: Optional[str] = None) -> List[Dict[str, Any]]:
        """收集 CryptoPanic 新聞數據"""
        data = []
        
        if not auth_token:
            logger.warning("未提供 CryptoPanic API token，跳過新聞收集")
            return data
        
        try:
            url = f"{CRYPTOPANIC_API_URL}/posts/"
            params = {
                'auth_token': auth_token,
                'kind': 'news',
                'filter': 'hot',
                'public': 'true'
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            news_data = response.json()
            
            for post in news_data.get('results', []):
                data.append({
                    'title': post.get('title', ''),
                    'url': post.get('url', ''),
                    'published_at': post.get('published_at', ''),
                    'domain': post.get('domain', ''),
                    'votes': post.get('votes', {}).get('positive', 0),
                    'currencies': [c.get('code') for c in post.get('currencies', [])],
                    'source': 'cryptopanic',
                    'collected_at': datetime.now()
                })
            
            logger.info(f"從 CryptoPanic 收集到 {len(data)} 條新聞")
            
        except Exception as e:
            logger.error(f"收集 CryptoPanic 數據時發生錯誤: {e}")
        
        return data
