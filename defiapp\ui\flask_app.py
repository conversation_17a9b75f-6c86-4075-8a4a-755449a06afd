"""
Flask Web 應用
"""
import json
from flask import Flask, render_template, jsonify, request
from datetime import datetime, timedelta
import plotly.graph_objs as go
import plotly.utils
from defiapp.database.models import DatabaseManager
from defiapp.data_collector.collector import DataCollector
from defiapp.analyzer.engine import AnalysisEngine
from defiapp.analyzer.quantitative_model import QuantitativeModel

app = Flask(__name__)
app.config['SECRET_KEY'] = 'defi-research-secret-key'

# 初始化組件
db_manager = DatabaseManager()
data_collector = DataCollector()
analysis_engine = AnalysisEngine()
quant_model = QuantitativeModel()


@app.route('/')
def index():
    """首頁"""
    return render_template('index.html')


@app.route('/api/dashboard')
def dashboard_data():
    """儀表板數據 API"""
    try:
        # 獲取最近的新聞
        recent_news = db_manager.get_recent_news(limit=10)
        
        # 獲取 TVL 排行
        top_protocols = db_manager.get_top_protocols_by_tvl(limit=10)
        
        # 生成市場摘要
        market_data = {
            'news': recent_news,
            'tvl': top_protocols
        }
        market_summary = analysis_engine.generate_market_summary(market_data)
        
        return jsonify({
            'success': True,
            'data': {
                'recent_news': recent_news,
                'top_protocols': top_protocols,
                'market_summary': market_summary
            }
        })
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/collect-data')
def collect_data():
    """手動觸發數據收集"""
    try:
        # 收集數據
        collected_data = data_collector.collect_all_data()
        
        # 存儲到資料庫
        news_count = db_manager.insert_news(collected_data.get('news', []))
        price_count = db_manager.insert_price_data(collected_data.get('prices', []))
        tvl_count = db_manager.insert_tvl_data(collected_data.get('tvl', []))
        
        return jsonify({
            'success': True,
            'message': f'數據收集完成: 新聞 {news_count} 條, 價格 {price_count} 個, TVL {tvl_count} 個'
        })
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/analyze-news')
def analyze_news():
    """分析新聞情緒"""
    try:
        # 獲取未分析的新聞
        recent_news = db_manager.get_recent_news(limit=50)
        unanalyzed_news = [news for news in recent_news if not news.get('sentiment_score')]
        
        if not unanalyzed_news:
            return jsonify({'success': True, 'message': '沒有需要分析的新聞'})
        
        # 分析新聞
        analyzed_news = analysis_engine.analyze_news_batch(unanalyzed_news)
        
        # 更新資料庫 (這裡簡化處理)
        analysis_count = len(analyzed_news)
        
        return jsonify({
            'success': True,
            'message': f'完成 {analysis_count} 條新聞分析',
            'analyzed_news': analyzed_news[:5]  # 返回前5條作為示例
        })
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/price-chart/<coin_id>')
def price_chart(coin_id):
    """價格圖表數據"""
    try:
        # 獲取價格數據
        price_data = db_manager.get_price_trends(coin_id, days=30)
        
        if not price_data:
            return jsonify({'success': False, 'error': '沒有找到價格數據'})
        
        # 準備圖表數據
        dates = [item['collected_at'] for item in price_data]
        prices = [item['price_usd'] for item in price_data]
        
        # 創建 Plotly 圖表
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=prices,
            mode='lines+markers',
            name=f'{coin_id} Price',
            line=dict(color='#1f77b4', width=2)
        ))
        
        fig.update_layout(
            title=f'{coin_id.title()} Price Trend',
            xaxis_title='Date',
            yaxis_title='Price (USD)',
            hovermode='x unified'
        )
        
        # 轉換為 JSON
        graphJSON = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
        
        return jsonify({
            'success': True,
            'chart': graphJSON
        })
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/sentiment-analysis')
def sentiment_analysis():
    """情緒分析統計"""
    try:
        recent_news = db_manager.get_recent_news(limit=100)
        
        # 統計情緒分布
        sentiment_counts = {'positive': 0, 'negative': 0, 'neutral': 0}
        sentiment_scores = []
        
        for news in recent_news:
            if 'sentiment_score' in news and news['sentiment_score'] is not None:
                score = news['sentiment_score']
                sentiment_scores.append(score)
                
                if score > 0.1:
                    sentiment_counts['positive'] += 1
                elif score < -0.1:
                    sentiment_counts['negative'] += 1
                else:
                    sentiment_counts['neutral'] += 1
        
        # 計算平均情緒
        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
        
        return jsonify({
            'success': True,
            'data': {
                'sentiment_distribution': sentiment_counts,
                'average_sentiment': avg_sentiment,
                'total_analyzed': len(sentiment_scores)
            }
        })
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


def create_app():
    """創建 Flask 應用"""
    # 初始化資料庫
    db_manager.init_database()
    
    return app


if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
