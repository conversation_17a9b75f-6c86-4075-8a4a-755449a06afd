"""
排程系統
"""
import logging
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from defiapp.data_collector.collector import DataCollector
from defiapp.database.models import DatabaseManager
from defiapp.analyzer.engine import AnalysisEngine
from defiapp.notifier.line_notify import NotificationManager

logger = logging.getLogger(__name__)


class TaskScheduler:
    """任務排程器"""
    
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.data_collector = DataCollector()
        self.db_manager = DatabaseManager()
        self.analysis_engine = AnalysisEngine()
        self.notification_manager = NotificationManager()
        
        # 設置排程任務
        self._setup_jobs()
    
    def _setup_jobs(self):
        """設置排程任務"""
        try:
            # 每小時收集數據
            self.scheduler.add_job(
                func=self.collect_data_job,
                trigger=CronTrigger(minute=0),  # 每小時的0分執行
                id='collect_data',
                name='數據收集任務',
                replace_existing=True
            )
            
            # 每2小時分析新聞
            self.scheduler.add_job(
                func=self.analyze_news_job,
                trigger=CronTrigger(minute=30, hour='*/2'),  # 每2小時的30分執行
                id='analyze_news',
                name='新聞分析任務',
                replace_existing=True
            )
            
            # 每天早上8點發送市場摘要
            self.scheduler.add_job(
                func=self.send_daily_summary,
                trigger=CronTrigger(hour=8, minute=0),  # 每天8:00執行
                id='daily_summary',
                name='每日市場摘要',
                replace_existing=True
            )
            
            # 每天晚上8點發送投資建議
            self.scheduler.add_job(
                func=self.send_investment_recommendations,
                trigger=CronTrigger(hour=20, minute=0),  # 每天20:00執行
                id='investment_recommendations',
                name='投資建議推送',
                replace_existing=True
            )
            
            # 每15分鐘檢查重要新聞
            self.scheduler.add_job(
                func=self.check_important_news,
                trigger=CronTrigger(minute='*/15'),  # 每15分鐘執行
                id='check_news',
                name='重要新聞檢查',
                replace_existing=True
            )
            
            logger.info("排程任務設置完成")
            
        except Exception as e:
            logger.error(f"設置排程任務失敗: {e}")
    
    def collect_data_job(self):
        """數據收集任務"""
        try:
            logger.info("開始執行數據收集任務")
            
            # 收集數據
            collected_data = self.data_collector.collect_all_data()
            
            # 存儲到資料庫
            news_count = self.db_manager.insert_news(collected_data.get('news', []))
            price_count = self.db_manager.insert_price_data(collected_data.get('prices', []))
            tvl_count = self.db_manager.insert_tvl_data(collected_data.get('tvl', []))
            
            logger.info(f"數據收集完成: 新聞 {news_count} 條, 價格 {price_count} 個, TVL {tvl_count} 個")
            
        except Exception as e:
            logger.error(f"數據收集任務執行失敗: {e}")
    
    def analyze_news_job(self):
        """新聞分析任務"""
        try:
            logger.info("開始執行新聞分析任務")
            
            # 獲取未分析的新聞
            recent_news = self.db_manager.get_recent_news(limit=100)
            unanalyzed_news = [news for news in recent_news if not news.get('sentiment_score')]
            
            if not unanalyzed_news:
                logger.info("沒有需要分析的新聞")
                return
            
            # 分析新聞
            analyzed_news = self.analysis_engine.analyze_news_batch(unanalyzed_news)
            
            logger.info(f"完成 {len(analyzed_news)} 條新聞分析")
            
            # 這裡可以添加更新資料庫的邏輯
            
        except Exception as e:
            logger.error(f"新聞分析任務執行失敗: {e}")
    
    def send_daily_summary(self):
        """發送每日市場摘要"""
        try:
            logger.info("開始發送每日市場摘要")
            
            # 獲取市場數據
            recent_news = self.db_manager.get_recent_news(limit=50)
            top_protocols = self.db_manager.get_top_protocols_by_tvl(limit=20)
            
            market_data = {
                'news': recent_news,
                'tvl': top_protocols
            }
            
            # 生成市場摘要
            market_summary = self.analysis_engine.generate_market_summary(market_data)
            
            # 發送通知
            results = self.notification_manager.send_to_all('market_summary', market_summary)
            
            logger.info(f"市場摘要發送結果: {results}")
            
        except Exception as e:
            logger.error(f"發送每日市場摘要失敗: {e}")
    
    def send_investment_recommendations(self):
        """發送投資建議"""
        try:
            logger.info("開始發送投資建議")
            
            # 這裡可以實作更複雜的投資建議邏輯
            # 目前簡化為發送一個示例建議
            
            sample_recommendation = {
                'project_name': 'DeFi Portfolio',
                'investment_score': 0.3,
                'recommendation': 'Hold',
                'predicted_return_7d': 0.05,
                'risk_level': 'Medium',
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            results = self.notification_manager.send_to_all('investment_alert', sample_recommendation)
            
            logger.info(f"投資建議發送結果: {results}")
            
        except Exception as e:
            logger.error(f"發送投資建議失敗: {e}")
    
    def check_important_news(self):
        """檢查重要新聞"""
        try:
            # 獲取最近15分鐘的新聞
            recent_news = self.db_manager.get_recent_news(limit=10)
            
            for news in recent_news:
                # 檢查是否為重要新聞 (高情緒分數或包含重要關鍵字)
                sentiment_score = news.get('sentiment_score', 0)
                title = news.get('title', '').lower()
                
                is_important = (
                    abs(sentiment_score) > 0.5 or  # 高情緒分數
                    any(keyword in title for keyword in ['hack', 'exploit', 'partnership', 'launch', 'listing'])
                )
                
                if is_important:
                    # 發送重要新聞提醒
                    self.notification_manager.telegram.send_news_alert(news)
                    logger.info(f"發送重要新聞提醒: {news.get('title', '')}")
            
        except Exception as e:
            logger.error(f"檢查重要新聞失敗: {e}")
    
    def start(self):
        """啟動排程器"""
        try:
            self.scheduler.start()
            logger.info("排程器已啟動")
        except Exception as e:
            logger.error(f"啟動排程器失敗: {e}")
    
    def stop(self):
        """停止排程器"""
        try:
            self.scheduler.shutdown()
            logger.info("排程器已停止")
        except Exception as e:
            logger.error(f"停止排程器失敗: {e}")
    
    def get_jobs(self):
        """獲取所有任務狀態"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.strftime('%Y-%m-%d %H:%M:%S') if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        return jobs
