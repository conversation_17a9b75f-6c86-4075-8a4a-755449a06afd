"""
Twitter 認證工具
"""
import re
import logging
from typing import Dict, Optional, Tuple

logger = logging.getLogger(__name__)


def extract_twitter_cookies_from_browser_string(cookie_string: str) -> Tuple[Optional[str], Optional[str]]:
    """
    從瀏覽器 cookie 字符串中提取 Twitter 認證信息
    
    Args:
        cookie_string: 從瀏覽器複製的 cookie 字符串
        
    Returns:
        Tuple[auth_token, ct0]: 認證 token 和 CSRF token
    """
    auth_token = None
    ct0 = None
    
    try:
        # 提取 auth_token
        auth_match = re.search(r'auth_token=([^;]+)', cookie_string)
        if auth_match:
            auth_token = auth_match.group(1)
        
        # 提取 ct0 (CSRF token)
        ct0_match = re.search(r'ct0=([^;]+)', cookie_string)
        if ct0_match:
            ct0 = ct0_match.group(1)
        
        if auth_token and ct0:
            logger.info("成功提取 Twitter 認證信息")
            return auth_token, ct0
        else:
            logger.warning("未能提取完整的 Twitter 認證信息")
            return None, None
            
    except Exception as e:
        logger.error(f"提取 Twitter 認證信息時發生錯誤: {e}")
        return None, None


def get_twitter_cookies_instructions() -> str:
    """
    獲取如何從瀏覽器中提取 Twitter cookies 的說明
    """
    return """
如何獲取 Twitter 認證 Cookies:

1. 在瀏覽器中登錄 Twitter (https://twitter.com)

2. 打開開發者工具 (F12)

3. 進入 Network (網路) 標籤

4. 刷新頁面或點擊任何 Twitter 連結

5. 在請求列表中找到任何對 twitter.com 的請求

6. 點擊該請求，查看 Request Headers

7. 找到 Cookie 行，複製整個 cookie 字符串

8. 或者直接在 Application/Storage 標籤中找到 twitter.com 的 cookies:
   - 複製 auth_token 的值
   - 複製 ct0 的值

示例 cookie 字符串格式:
auth_token=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx; ct0=yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy; ...

注意: 請保護好您的認證信息，不要分享給他人！
"""


def validate_twitter_cookies(auth_token: str, ct0: str) -> bool:
    """
    驗證 Twitter cookies 格式是否正確
    
    Args:
        auth_token: Twitter 認證 token
        ct0: CSRF token
        
    Returns:
        bool: 是否格式正確
    """
    try:
        # 檢查 auth_token 格式 (通常是40個字符的十六進制字符串)
        if not auth_token or len(auth_token) < 30:
            logger.warning("auth_token 格式不正確或太短")
            return False
        
        # 檢查 ct0 格式 (通常是32個字符的十六進制字符串)
        if not ct0 or len(ct0) < 20:
            logger.warning("ct0 格式不正確或太短")
            return False
        
        # 檢查是否包含有效字符
        if not re.match(r'^[a-fA-F0-9]+$', auth_token):
            logger.warning("auth_token 包含無效字符")
            return False
        
        if not re.match(r'^[a-fA-F0-9]+$', ct0):
            logger.warning("ct0 包含無效字符")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"驗證 Twitter cookies 時發生錯誤: {e}")
        return False


def create_twitter_collector_with_cookies(auth_token: str, ct0: str):
    """
    使用 cookies 創建 Twitter 收集器
    
    Args:
        auth_token: Twitter 認證 token
        ct0: CSRF token
        
    Returns:
        TwitterCollector: 配置好的 Twitter 收集器
    """
    try:
        if not validate_twitter_cookies(auth_token, ct0):
            raise ValueError("Twitter cookies 格式不正確")
        
        from defiapp.data_collector.twitter_collector import TwitterCollector
        
        collector = TwitterCollector(
            bearer_token=None,
            auth_token=auth_token,
            ct0=ct0
        )
        
        logger.info("成功創建使用 cookies 認證的 Twitter 收集器")
        return collector
        
    except Exception as e:
        logger.error(f"創建 Twitter 收集器時發生錯誤: {e}")
        raise


# 示例使用方法
def example_usage():
    """
    示例使用方法
    """
    # 方法1: 從完整 cookie 字符串提取
    cookie_string = "auth_token=your_auth_token_here; ct0=your_ct0_here; other_cookies=..."
    auth_token, ct0 = extract_twitter_cookies_from_browser_string(cookie_string)
    
    if auth_token and ct0:
        collector = create_twitter_collector_with_cookies(auth_token, ct0)
        tweets = collector.collect_user_tweets('uniswap', max_results=20)
        print(f"收集到 {len(tweets)} 條推文")
    
    # 方法2: 直接提供 tokens
    # auth_token = "your_auth_token_here"
    # ct0 = "your_ct0_here"
    # collector = create_twitter_collector_with_cookies(auth_token, ct0)


if __name__ == "__main__":
    print(get_twitter_cookies_instructions())
