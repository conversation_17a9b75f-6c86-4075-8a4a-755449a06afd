"""
Twitter 數據收集器
"""
import requests
import logging
import time
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from defiapp.config import TWITTER_BEARER_TOKEN

logger = logging.getLogger(__name__)


class TwitterCollector:
    """Twitter 數據收集器"""

    def __init__(self, bearer_token: str = None, auth_token: str = None, ct0: str = None):
        self.bearer_token = bearer_token or TWITTER_BEARER_TOKEN
        self.auth_token = auth_token
        self.ct0 = ct0
        self.base_url = "https://api.twitter.com/2"
        self.web_api_url = "https://twitter.com/i/api/graphql"

        # 設置認證方式
        if self.auth_token and self.ct0:
            # 使用 cookie 認證 (Web API)
            self.use_web_api = True
            self.headers = {
                "Authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
                "Content-Type": "application/json",
                "X-Csrf-Token": self.ct0,
                "Cookie": f"auth_token={self.auth_token}; ct0={self.ct0}",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
            logger.info("使用 Cookie 認證模式")
        elif self.bearer_token:
            # 使用 Bearer Token (官方 API)
            self.use_web_api = False
            self.headers = {
                "Authorization": f"Bearer {self.bearer_token}",
                "Content-Type": "application/json"
            }
            logger.info("使用 Bearer Token 認證模式")
        else:
            self.use_web_api = False
            self.headers = None
            logger.warning("未設置任何認證方式。請設置 TWITTER_BEARER_TOKEN 或提供 auth_token 和 ct0")

        # 檢查 token 狀態
        if self.bearer_token and len(self.bearer_token) < 50:
            logger.warning("Twitter Bearer Token 似乎太短，請檢查是否正確")
        
        # 重要的 DeFi KOL 和項目官方帳號
        self.defi_accounts = {
            # DeFi 項目官方
            'uniswap': 'Uniswap',
            'aaveaave': 'Aave',
            'compoundfinance': 'Compound',
            'makerdao': 'MakerDAO',
            'curvefinance': 'Curve Finance',
            'yearnfinance': 'Yearn Finance',
            'sushiswap': 'SushiSwap',
            'pancakeswap': 'PancakeSwap',
            '1inch': '1inch',
            'synthetix_io': 'Synthetix',
            
            # DeFi KOL 和分析師
            'defillama': 'DefiLlama',
            'cryptorank_io': 'CryptoRank',
            'haydenzadams': 'Hayden Adams (Uniswap)',
            'stani': 'Stani Kulechov (Aave)',
            'rleshner': 'Robert Leshner (Compound)',
            'runekek': 'Rune Christensen (MakerDAO)',
            'bantg': 'Banteg (Yearn)',
            'delphi_digital': 'Delphi Digital',
            'messaricrypto': 'Messari',
            'thedefiedge': 'The DeFi Edge',
            'defiminute': 'DeFi Minute'
        }
        
        # DeFi 相關關鍵字
        self.defi_keywords = [
            'DeFi', 'yield farming', 'liquidity mining', 'staking',
            'DEX', 'AMM', 'lending', 'borrowing', 'governance',
            'TVL', 'APY', 'impermanent loss', 'flash loan',
            'liquidity pool', 'farming', 'protocol', 'token unlock',
            'airdrop', 'governance token', 'DAO'
        ]
    
    def collect_user_tweets(self, username: str, max_results: int = 100) -> List[Dict[str, Any]]:
        """收集特定用戶的推文"""
        if not self.headers:
            logger.warning("未設置認證信息，跳過 Twitter 數據收集")
            return []

        try:
            if self.use_web_api:
                return self._collect_user_tweets_web_api(username, max_results)
            else:
                return self._collect_user_tweets_official_api(username, max_results)
        except Exception as e:
            logger.error(f"收集 @{username} 推文時發生錯誤: {e}")
            return []

    def _collect_user_tweets_web_api(self, username: str, max_results: int = 100) -> List[Dict[str, Any]]:
        """使用 Web API 收集用戶推文"""
        try:
            # 使用 Twitter Web API 的 UserTweets GraphQL 端點
            url = f"{self.web_api_url}/V7uyXym_RHPbZLd9b_Tw7w/UserTweets"

            # 獲取用戶信息
            user_info = self._get_user_info_web_api(username)
            if not user_info:
                return []

            user_id = user_info.get('rest_id')
            if not user_id:
                return []

            variables = {
                "userId": user_id,
                "count": min(max_results, 40),  # Web API 限制
                "includePromotedContent": False,
                "withQuickPromoteEligibilityTweetFields": False,
                "withVoice": False,
                "withV2Timeline": True
            }

            features = {
                "rweb_lists_timeline_redesign_enabled": True,
                "responsive_web_graphql_exclude_directive_enabled": True,
                "verified_phone_label_enabled": False,
                "creator_subscriptions_tweet_preview_api_enabled": True,
                "responsive_web_graphql_timeline_navigation_enabled": True,
                "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
                "tweetypie_unmention_optimization_enabled": True,
                "responsive_web_edit_tweet_api_enabled": True,
                "graphql_is_translatable_rweb_tweet_is_translatable_enabled": True,
                "view_counts_everywhere_api_enabled": True,
                "longform_notetweets_consumption_enabled": True,
                "responsive_web_twitter_article_tweet_consumption_enabled": False,
                "tweet_awards_web_tipping_enabled": False,
                "freedom_of_speech_not_reach_fetch_enabled": True,
                "standardized_nudges_misinfo": True,
                "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": True,
                "longform_notetweets_rich_text_read_enabled": True,
                "longform_notetweets_inline_media_enabled": True,
                "responsive_web_media_download_video_enabled": False,
                "responsive_web_enhance_cards_enabled": False
            }

            params = {
                'variables': json.dumps(variables),
                'features': json.dumps(features)
            }

            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            data = response.json()
            tweets = []

            # 解析 GraphQL 響應
            if 'data' in data and 'user' in data['data'] and 'result' in data['data']['user']:
                timeline = data['data']['user']['result'].get('timeline_v2', {}).get('timeline', {})
                instructions = timeline.get('instructions', [])

                for instruction in instructions:
                    if instruction.get('type') == 'TimelineAddEntries':
                        entries = instruction.get('entries', [])
                        for entry in entries:
                            if entry.get('entryId', '').startswith('tweet-'):
                                tweet_data = self._extract_tweet_from_entry(entry)
                                if tweet_data:
                                    processed_tweet = self._process_web_api_tweet(tweet_data, username)
                                    if processed_tweet:
                                        tweets.append(processed_tweet)

            logger.info(f"從 @{username} 收集到 {len(tweets)} 條推文 (Web API)")
            return tweets

        except Exception as e:
            logger.error(f"使用 Web API 收集 @{username} 推文時發生錯誤: {e}")
            return []

    def _collect_user_tweets_official_api(self, username: str, max_results: int = 100) -> List[Dict[str, Any]]:
        """使用官方 API 收集用戶推文"""
        try:
            # 獲取用戶 ID
            user_id = self._get_user_id(username)
            if not user_id:
                return []

            # 獲取用戶推文
            url = f"{self.base_url}/users/{user_id}/tweets"
            params = {
                'max_results': min(max_results, 100),  # API 限制
                'tweet.fields': 'created_at,public_metrics,context_annotations,lang,referenced_tweets',
                'expansions': 'referenced_tweets.id',
                'exclude': 'retweets'  # 排除轉推
            }

            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            data = response.json()
            tweets = []

            if 'data' in data:
                for tweet in data['data']:
                    # 只處理英文和中文推文
                    if tweet.get('lang') in ['en', 'zh', 'zh-cn', 'zh-tw']:
                        processed_tweet = self._process_tweet(tweet, username)
                        if processed_tweet:
                            tweets.append(processed_tweet)

            logger.info(f"從 @{username} 收集到 {len(tweets)} 條推文 (官方 API)")
            return tweets

        except Exception as e:
            logger.error(f"使用官方 API 收集 @{username} 推文時發生錯誤: {e}")
            return []
    
    def collect_keyword_tweets(self, keyword: str, max_results: int = 100) -> List[Dict[str, Any]]:
        """根據關鍵字搜索推文"""
        if not self.bearer_token:
            logger.warning("Twitter Bearer Token 未設置，跳過關鍵字搜索")
            return []
        
        try:
            url = f"{self.base_url}/tweets/search/recent"
            
            # 構建搜索查詢
            query = f"{keyword} -is:retweet lang:en OR lang:zh"
            
            params = {
                'query': query,
                'max_results': min(max_results, 100),
                'tweet.fields': 'created_at,public_metrics,context_annotations,lang,author_id',
                'user.fields': 'username,name,verified,public_metrics',
                'expansions': 'author_id'
            }
            
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            tweets = []
            
            # 建立用戶映射
            users_map = {}
            if 'includes' in data and 'users' in data['includes']:
                for user in data['includes']['users']:
                    users_map[user['id']] = user
            
            if 'data' in data:
                for tweet in data['data']:
                    processed_tweet = self._process_tweet(tweet, None, users_map)
                    if processed_tweet:
                        tweets.append(processed_tweet)
            
            logger.info(f"關鍵字 '{keyword}' 搜索到 {len(tweets)} 條推文")
            return tweets
            
        except Exception as e:
            logger.error(f"搜索關鍵字 '{keyword}' 時發生錯誤: {e}")
            return []
    
    def collect_trending_defi_tweets(self) -> List[Dict[str, Any]]:
        """收集熱門 DeFi 推文"""
        all_tweets = []
        
        # 收集重要帳號的推文
        for username, display_name in list(self.defi_accounts.items())[:10]:  # 限制請求數量
            tweets = self.collect_user_tweets(username, max_results=20)
            all_tweets.extend(tweets)
            time.sleep(1)  # 避免 API 限制
        
        # 收集關鍵字相關推文
        for keyword in self.defi_keywords[:5]:  # 限制關鍵字數量
            tweets = self.collect_keyword_tweets(keyword, max_results=20)
            all_tweets.extend(tweets)
            time.sleep(1)  # 避免 API 限制
        
        # 去重並按互動數排序
        unique_tweets = self._deduplicate_tweets(all_tweets)
        sorted_tweets = sorted(unique_tweets, 
                             key=lambda x: x.get('engagement_score', 0), 
                             reverse=True)
        
        return sorted_tweets[:100]  # 返回前100條
    
    def _get_user_id(self, username: str) -> Optional[str]:
        """獲取用戶 ID"""
        try:
            url = f"{self.base_url}/users/by/username/{username}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            data = response.json()
            return data.get('data', {}).get('id')
            
        except Exception as e:
            logger.error(f"獲取用戶 @{username} ID 失敗: {e}")
            return None
    
    def _process_tweet(self, tweet: Dict[str, Any], username: str = None, 
                      users_map: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """處理推文數據"""
        try:
            text = tweet.get('text', '')
            
            # 檢查是否包含 DeFi 相關內容
            if not self._is_defi_related(text):
                return None
            
            # 獲取用戶信息
            author_info = {}
            if username:
                author_info = {
                    'username': username,
                    'display_name': self.defi_accounts.get(username, username)
                }
            elif users_map and tweet.get('author_id'):
                user_data = users_map.get(tweet['author_id'], {})
                author_info = {
                    'username': user_data.get('username', ''),
                    'display_name': user_data.get('name', ''),
                    'verified': user_data.get('verified', False),
                    'followers_count': user_data.get('public_metrics', {}).get('followers_count', 0)
                }
            
            # 計算互動分數
            metrics = tweet.get('public_metrics', {})
            engagement_score = (
                metrics.get('like_count', 0) * 1 +
                metrics.get('retweet_count', 0) * 3 +
                metrics.get('reply_count', 0) * 2 +
                metrics.get('quote_count', 0) * 2
            )
            
            processed_tweet = {
                'tweet_id': tweet.get('id'),
                'text': text,
                'created_at': tweet.get('created_at'),
                'author': author_info,
                'metrics': metrics,
                'engagement_score': engagement_score,
                'language': tweet.get('lang'),
                'source': 'twitter',
                'collected_at': datetime.now(),
                'url': f"https://twitter.com/{author_info.get('username', 'unknown')}/status/{tweet.get('id')}"
            }
            
            return processed_tweet
            
        except Exception as e:
            logger.error(f"處理推文時發生錯誤: {e}")
            return None
    
    def _is_defi_related(self, text: str) -> bool:
        """檢查推文是否與 DeFi 相關"""
        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in self.defi_keywords)
    
    def _deduplicate_tweets(self, tweets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去除重複推文"""
        seen_ids = set()
        unique_tweets = []
        
        for tweet in tweets:
            tweet_id = tweet.get('tweet_id')
            if tweet_id and tweet_id not in seen_ids:
                seen_ids.add(tweet_id)
                unique_tweets.append(tweet)
        
        return unique_tweets

    def _get_user_info_web_api(self, username: str) -> Optional[Dict[str, Any]]:
        """使用 Web API 獲取用戶信息"""
        try:
            url = f"{self.web_api_url}/sLVLhk0bGj3MVFEKTdax1w/UserByScreenName"

            variables = {
                "screen_name": username,
                "withSafetyModeUserFields": True
            }

            features = {
                "hidden_profile_likes_enabled": True,
                "responsive_web_graphql_exclude_directive_enabled": True,
                "verified_phone_label_enabled": False,
                "subscriptions_verification_info_is_identity_verified_enabled": True,
                "subscriptions_verification_info_verified_since_enabled": True,
                "highlights_tweets_tab_ui_enabled": True,
                "creator_subscriptions_tweet_preview_api_enabled": True,
                "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
                "responsive_web_graphql_timeline_navigation_enabled": True
            }

            params = {
                'variables': json.dumps(variables),
                'features': json.dumps(features)
            }

            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            data = response.json()

            if 'data' in data and 'user' in data['data'] and 'result' in data['data']['user']:
                return data['data']['user']['result']

            return None

        except Exception as e:
            logger.error(f"獲取用戶 @{username} 信息失敗 (Web API): {e}")
            return None

    def _extract_tweet_from_entry(self, entry: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """從 GraphQL entry 中提取推文數據"""
        try:
            content = entry.get('content', {})
            if content.get('entryType') == 'TimelineTimelineItem':
                item_content = content.get('itemContent', {})
                if item_content.get('itemType') == 'TimelineTweet':
                    tweet_results = item_content.get('tweet_results', {})
                    if 'result' in tweet_results:
                        return tweet_results['result']
            return None
        except Exception:
            return None

    def _process_web_api_tweet(self, tweet_data: Dict[str, Any], username: str = None) -> Optional[Dict[str, Any]]:
        """處理 Web API 推文數據"""
        try:
            legacy = tweet_data.get('legacy', {})
            text = legacy.get('full_text', '')

            # 檢查是否包含 DeFi 相關內容
            if not self._is_defi_related(text):
                return None

            # 獲取用戶信息
            user_results = tweet_data.get('core', {}).get('user_results', {})
            user_legacy = user_results.get('result', {}).get('legacy', {})

            author_info = {
                'username': user_legacy.get('screen_name', username or ''),
                'display_name': user_legacy.get('name', ''),
                'verified': user_legacy.get('verified', False),
                'followers_count': user_legacy.get('followers_count', 0)
            }

            # 計算互動分數
            favorite_count = legacy.get('favorite_count', 0)
            retweet_count = legacy.get('retweet_count', 0)
            reply_count = legacy.get('reply_count', 0)
            quote_count = legacy.get('quote_count', 0)

            engagement_score = (
                favorite_count * 1 +
                retweet_count * 3 +
                reply_count * 2 +
                quote_count * 2
            )

            processed_tweet = {
                'tweet_id': legacy.get('id_str'),
                'text': text,
                'created_at': legacy.get('created_at'),
                'author': author_info,
                'metrics': {
                    'like_count': favorite_count,
                    'retweet_count': retweet_count,
                    'reply_count': reply_count,
                    'quote_count': quote_count
                },
                'engagement_score': engagement_score,
                'language': legacy.get('lang'),
                'source': 'twitter_web_api',
                'collected_at': datetime.now(),
                'url': f"https://twitter.com/{author_info.get('username', 'unknown')}/status/{legacy.get('id_str')}"
            }

            return processed_tweet

        except Exception as e:
            logger.error(f"處理 Web API 推文時發生錯誤: {e}")
            return None
    
    def test_api_connection(self) -> Dict[str, Any]:
        """測試 API 連接"""
        if not self.headers:
            return {
                'success': False,
                'error': '未設置認證信息',
                'suggestion': '請設置 TWITTER_BEARER_TOKEN 或提供 auth_token 和 ct0'
            }

        try:
            if self.use_web_api:
                return self._test_web_api_connection()
            else:
                return self._test_official_api_connection()

        except Exception as e:
            return {
                'success': False,
                'error': f'連接錯誤: {str(e)}',
                'suggestion': '請檢查網路連接和 API 設置'
            }

    def _test_web_api_connection(self) -> Dict[str, Any]:
        """測試 Web API 連接"""
        try:
            # 測試獲取用戶信息
            user_info = self._get_user_info_web_api('twitter')

            if user_info:
                return {
                    'success': True,
                    'message': 'Twitter Web API 連接成功',
                    'method': 'Cookie 認證',
                    'user_info': {
                        'screen_name': user_info.get('legacy', {}).get('screen_name'),
                        'name': user_info.get('legacy', {}).get('name')
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'Web API 認證失敗',
                    'suggestion': '請檢查 auth_token 和 ct0 是否正確'
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'Web API 測試失敗: {str(e)}',
                'suggestion': '請檢查 cookie 認證信息是否正確'
            }

    def _test_official_api_connection(self) -> Dict[str, Any]:
        """測試官方 API 連接"""
        try:
            # 使用簡單的 API 端點測試連接
            url = f"{self.base_url}/tweets/search/recent"
            params = {
                'query': 'hello',
                'max_results': 10
            }

            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                return {
                    'success': True,
                    'message': 'Twitter 官方 API 連接成功',
                    'method': 'Bearer Token',
                    'rate_limit': {
                        'remaining': response.headers.get('x-rate-limit-remaining'),
                        'reset': response.headers.get('x-rate-limit-reset'),
                        'limit': response.headers.get('x-rate-limit-limit')
                    }
                }
            elif response.status_code == 401:
                return {
                    'success': False,
                    'error': 'Bearer token 無效或已過期',
                    'suggestion': '請檢查 Twitter Developer Portal 中的 Bearer Token'
                }
            elif response.status_code == 429:
                return {
                    'success': False,
                    'error': 'API 請求限制已達上限',
                    'suggestion': '請稍後再試'
                }
            else:
                return {
                    'success': False,
                    'error': f'API 請求失敗: {response.status_code}',
                    'response': response.text[:200]
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'官方 API 測試失敗: {str(e)}',
                'suggestion': '請檢查 Bearer Token 是否正確'
            }

    def get_rate_limit_status(self) -> Dict[str, Any]:
        """檢查 API 限制狀態"""
        if not self.bearer_token:
            return {'error': 'No bearer token'}

        try:
            url = f"{self.base_url}/tweets/search/recent"
            response = requests.get(url, headers=self.headers, params={'query': 'test', 'max_results': 10})

            return {
                'remaining': response.headers.get('x-rate-limit-remaining'),
                'reset': response.headers.get('x-rate-limit-reset'),
                'limit': response.headers.get('x-rate-limit-limit'),
                'status_code': response.status_code
            }

        except Exception as e:
            return {'error': str(e)}
