"""
Twitter 數據收集器
"""
import requests
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from defiapp.config import TWITTER_BEARER_TOKEN

logger = logging.getLogger(__name__)


class TwitterCollector:
    """Twitter 數據收集器"""
    
    def __init__(self, bearer_token: str = None):
        self.bearer_token = bearer_token or TWITTER_BEARER_TOKEN
        self.base_url = "https://api.twitter.com/2"
        self.headers = {
            "Authorization": f"Bearer {self.bearer_token}",
            "Content-Type": "application/json"
        } if self.bearer_token else None
        
        # 重要的 DeFi KOL 和項目官方帳號
        self.defi_accounts = {
            # DeFi 項目官方
            'uniswap': 'Uniswap',
            'aaveaave': 'Aave',
            'compoundfinance': 'Compound',
            'makerdao': 'MakerDAO',
            'curvefinance': 'Curve Finance',
            'yearnfinance': 'Yearn Finance',
            'sushiswap': 'SushiSwap',
            'pancakeswap': 'PancakeSwap',
            '1inch': '1inch',
            'synthetix_io': 'Synthetix',
            
            # DeFi KOL 和分析師
            'defillama': 'DefiLlama',
            'cryptorank_io': 'CryptoRank',
            'haydenzadams': 'Hayden Adams (Uniswap)',
            'stani': 'Stani Kulechov (Aave)',
            'rleshner': 'Robert Leshner (Compound)',
            'runekek': 'Rune Christensen (MakerDAO)',
            'bantg': 'Banteg (Yearn)',
            'delphi_digital': 'Delphi Digital',
            'messaricrypto': 'Messari',
            'thedefiedge': 'The DeFi Edge',
            'defiminute': 'DeFi Minute'
        }
        
        # DeFi 相關關鍵字
        self.defi_keywords = [
            'DeFi', 'yield farming', 'liquidity mining', 'staking',
            'DEX', 'AMM', 'lending', 'borrowing', 'governance',
            'TVL', 'APY', 'impermanent loss', 'flash loan',
            'liquidity pool', 'farming', 'protocol', 'token unlock',
            'airdrop', 'governance token', 'DAO'
        ]
    
    def collect_user_tweets(self, username: str, max_results: int = 100) -> List[Dict[str, Any]]:
        """收集特定用戶的推文"""
        if not self.bearer_token:
            logger.warning("Twitter Bearer Token 未設置，跳過 Twitter 數據收集")
            return []
        
        try:
            # 獲取用戶 ID
            user_id = self._get_user_id(username)
            if not user_id:
                return []
            
            # 獲取用戶推文
            url = f"{self.base_url}/users/{user_id}/tweets"
            params = {
                'max_results': min(max_results, 100),  # API 限制
                'tweet.fields': 'created_at,public_metrics,context_annotations,lang,referenced_tweets',
                'expansions': 'referenced_tweets.id',
                'exclude': 'retweets'  # 排除轉推
            }
            
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            tweets = []
            
            if 'data' in data:
                for tweet in data['data']:
                    # 只處理英文和中文推文
                    if tweet.get('lang') in ['en', 'zh', 'zh-cn', 'zh-tw']:
                        processed_tweet = self._process_tweet(tweet, username)
                        if processed_tweet:
                            tweets.append(processed_tweet)
            
            logger.info(f"從 @{username} 收集到 {len(tweets)} 條推文")
            return tweets
            
        except Exception as e:
            logger.error(f"收集 @{username} 推文時發生錯誤: {e}")
            return []
    
    def collect_keyword_tweets(self, keyword: str, max_results: int = 100) -> List[Dict[str, Any]]:
        """根據關鍵字搜索推文"""
        if not self.bearer_token:
            logger.warning("Twitter Bearer Token 未設置，跳過關鍵字搜索")
            return []
        
        try:
            url = f"{self.base_url}/tweets/search/recent"
            
            # 構建搜索查詢
            query = f"{keyword} -is:retweet lang:en OR lang:zh"
            
            params = {
                'query': query,
                'max_results': min(max_results, 100),
                'tweet.fields': 'created_at,public_metrics,context_annotations,lang,author_id',
                'user.fields': 'username,name,verified,public_metrics',
                'expansions': 'author_id'
            }
            
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            tweets = []
            
            # 建立用戶映射
            users_map = {}
            if 'includes' in data and 'users' in data['includes']:
                for user in data['includes']['users']:
                    users_map[user['id']] = user
            
            if 'data' in data:
                for tweet in data['data']:
                    processed_tweet = self._process_tweet(tweet, None, users_map)
                    if processed_tweet:
                        tweets.append(processed_tweet)
            
            logger.info(f"關鍵字 '{keyword}' 搜索到 {len(tweets)} 條推文")
            return tweets
            
        except Exception as e:
            logger.error(f"搜索關鍵字 '{keyword}' 時發生錯誤: {e}")
            return []
    
    def collect_trending_defi_tweets(self) -> List[Dict[str, Any]]:
        """收集熱門 DeFi 推文"""
        all_tweets = []
        
        # 收集重要帳號的推文
        for username, display_name in list(self.defi_accounts.items())[:10]:  # 限制請求數量
            tweets = self.collect_user_tweets(username, max_results=20)
            all_tweets.extend(tweets)
            time.sleep(1)  # 避免 API 限制
        
        # 收集關鍵字相關推文
        for keyword in self.defi_keywords[:5]:  # 限制關鍵字數量
            tweets = self.collect_keyword_tweets(keyword, max_results=20)
            all_tweets.extend(tweets)
            time.sleep(1)  # 避免 API 限制
        
        # 去重並按互動數排序
        unique_tweets = self._deduplicate_tweets(all_tweets)
        sorted_tweets = sorted(unique_tweets, 
                             key=lambda x: x.get('engagement_score', 0), 
                             reverse=True)
        
        return sorted_tweets[:100]  # 返回前100條
    
    def _get_user_id(self, username: str) -> Optional[str]:
        """獲取用戶 ID"""
        try:
            url = f"{self.base_url}/users/by/username/{username}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            data = response.json()
            return data.get('data', {}).get('id')
            
        except Exception as e:
            logger.error(f"獲取用戶 @{username} ID 失敗: {e}")
            return None
    
    def _process_tweet(self, tweet: Dict[str, Any], username: str = None, 
                      users_map: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """處理推文數據"""
        try:
            text = tweet.get('text', '')
            
            # 檢查是否包含 DeFi 相關內容
            if not self._is_defi_related(text):
                return None
            
            # 獲取用戶信息
            author_info = {}
            if username:
                author_info = {
                    'username': username,
                    'display_name': self.defi_accounts.get(username, username)
                }
            elif users_map and tweet.get('author_id'):
                user_data = users_map.get(tweet['author_id'], {})
                author_info = {
                    'username': user_data.get('username', ''),
                    'display_name': user_data.get('name', ''),
                    'verified': user_data.get('verified', False),
                    'followers_count': user_data.get('public_metrics', {}).get('followers_count', 0)
                }
            
            # 計算互動分數
            metrics = tweet.get('public_metrics', {})
            engagement_score = (
                metrics.get('like_count', 0) * 1 +
                metrics.get('retweet_count', 0) * 3 +
                metrics.get('reply_count', 0) * 2 +
                metrics.get('quote_count', 0) * 2
            )
            
            processed_tweet = {
                'tweet_id': tweet.get('id'),
                'text': text,
                'created_at': tweet.get('created_at'),
                'author': author_info,
                'metrics': metrics,
                'engagement_score': engagement_score,
                'language': tweet.get('lang'),
                'source': 'twitter',
                'collected_at': datetime.now(),
                'url': f"https://twitter.com/{author_info.get('username', 'unknown')}/status/{tweet.get('id')}"
            }
            
            return processed_tweet
            
        except Exception as e:
            logger.error(f"處理推文時發生錯誤: {e}")
            return None
    
    def _is_defi_related(self, text: str) -> bool:
        """檢查推文是否與 DeFi 相關"""
        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in self.defi_keywords)
    
    def _deduplicate_tweets(self, tweets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去除重複推文"""
        seen_ids = set()
        unique_tweets = []
        
        for tweet in tweets:
            tweet_id = tweet.get('tweet_id')
            if tweet_id and tweet_id not in seen_ids:
                seen_ids.add(tweet_id)
                unique_tweets.append(tweet)
        
        return unique_tweets
    
    def get_rate_limit_status(self) -> Dict[str, Any]:
        """檢查 API 限制狀態"""
        if not self.bearer_token:
            return {'error': 'No bearer token'}
        
        try:
            url = f"{self.base_url}/tweets/search/recent"
            response = requests.get(url, headers=self.headers, params={'query': 'test', 'max_results': 10})
            
            return {
                'remaining': response.headers.get('x-rate-limit-remaining'),
                'reset': response.headers.get('x-rate-limit-reset'),
                'limit': response.headers.get('x-rate-limit-limit')
            }
            
        except Exception as e:
            return {'error': str(e)}
