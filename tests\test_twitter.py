"""
Twitter 模組測試
"""
import unittest
from unittest.mock import Mock, patch
from defiapp.data_collector.twitter_collector import <PERSON><PERSON><PERSON>ector
from defiapp.analyzer.twitter_analyzer import TwitterAnalyzer


class TestTwitterCollector(unittest.TestCase):
    """Twitter 收集器測試"""
    
    def setUp(self):
        self.collector = TwitterCollector()
    
    def test_is_defi_related(self):
        """測試 DeFi 相關內容檢測"""
        defi_text = "Just discovered this amazing DeFi protocol with 100% APY!"
        non_defi_text = "Having a great day at the beach!"
        
        self.assertTrue(self.collector._is_defi_related(defi_text))
        self.assertFalse(self.collector._is_defi_related(non_defi_text))
    
    def test_process_tweet(self):
        """測試推文處理"""
        mock_tweet = {
            'id': '123456789',
            'text': 'Uniswap is the best DEX! 🚀 #DeFi #Uniswap',
            'created_at': '2024-01-01T12:00:00.000Z',
            'lang': 'en',
            'public_metrics': {
                'like_count': 100,
                'retweet_count': 50,
                'reply_count': 25,
                'quote_count': 10
            }
        }
        
        result = self.collector._process_tweet(mock_tweet, 'testuser')
        
        self.assertIsNotNone(result)
        self.assertEqual(result['tweet_id'], '123456789')
        self.assertIn('uniswap', result['text'].lower())
        self.assertGreater(result['engagement_score'], 0)
    
    def test_deduplicate_tweets(self):
        """測試推文去重"""
        tweets = [
            {'tweet_id': '123', 'text': 'Tweet 1'},
            {'tweet_id': '456', 'text': 'Tweet 2'},
            {'tweet_id': '123', 'text': 'Tweet 1 duplicate'},
            {'tweet_id': '789', 'text': 'Tweet 3'}
        ]
        
        unique_tweets = self.collector._deduplicate_tweets(tweets)
        
        self.assertEqual(len(unique_tweets), 3)
        tweet_ids = [t['tweet_id'] for t in unique_tweets]
        self.assertEqual(len(set(tweet_ids)), 3)


class TestTwitterAnalyzer(unittest.TestCase):
    """Twitter 分析器測試"""
    
    def setUp(self):
        self.analyzer = TwitterAnalyzer()
    
    def test_extract_hashtags(self):
        """測試 hashtag 提取"""
        text = "This is a great #DeFi project! #Uniswap #bullish"
        hashtags = self.analyzer._extract_hashtags(text)
        
        self.assertEqual(len(hashtags), 3)
        self.assertIn('DeFi', hashtags)
        self.assertIn('Uniswap', hashtags)
        self.assertIn('bullish', hashtags)
    
    def test_extract_mentions(self):
        """測試 mention 提取"""
        text = "Thanks @uniswap and @aaveaave for the great work!"
        mentions = self.analyzer._extract_mentions(text)
        
        self.assertEqual(len(mentions), 2)
        self.assertIn('uniswap', mentions)
        self.assertIn('aaveaave', mentions)
    
    def test_extract_cashtags(self):
        """測試 cashtag 提取"""
        text = "Bullish on $UNI and $AAVE! $ETH to the moon!"
        cashtags = self.analyzer._extract_cashtags(text)
        
        self.assertEqual(len(cashtags), 3)
        self.assertIn('UNI', cashtags)
        self.assertIn('AAVE', cashtags)
        self.assertIn('ETH', cashtags)
    
    def test_analyze_emoji_sentiment(self):
        """測試表情符號情緒分析"""
        positive_text = "DeFi is amazing! 🚀🌙💎"
        negative_text = "This project is dead 💀📉😭"
        neutral_text = "Just sharing some info about DeFi"
        
        positive_score = self.analyzer._analyze_emoji_sentiment(positive_text)
        negative_score = self.analyzer._analyze_emoji_sentiment(negative_text)
        neutral_score = self.analyzer._analyze_emoji_sentiment(neutral_text)
        
        self.assertGreater(positive_score, 0)
        self.assertLess(negative_score, 0)
        self.assertEqual(neutral_score, 0)
    
    def test_calculate_influence_score(self):
        """測試影響力分數計算"""
        # 高影響力用戶
        high_influence_author = {
            'verified': True,
            'followers_count': 100000
        }
        high_metrics = {
            'like_count': 1000,
            'retweet_count': 500,
            'reply_count': 200
        }
        
        # 低影響力用戶
        low_influence_author = {
            'verified': False,
            'followers_count': 100
        }
        low_metrics = {
            'like_count': 5,
            'retweet_count': 1,
            'reply_count': 2
        }
        
        high_score = self.analyzer._calculate_influence_score(high_influence_author, high_metrics)
        low_score = self.analyzer._calculate_influence_score(low_influence_author, low_metrics)
        
        self.assertGreater(high_score, low_score)
        self.assertGreater(high_score, 2.0)  # 應該有驗證和高粉絲數加成
    
    def test_detect_fomo_indicators(self):
        """測試 FOMO 指標檢測"""
        fomo_text = "Last chance to ape into this gem! Don't miss out! 100x potential!"
        normal_text = "Here's an analysis of the DeFi market trends"
        
        fomo_indicators = self.analyzer._detect_fomo_indicators(fomo_text)
        normal_indicators = self.analyzer._detect_fomo_indicators(normal_text)
        
        self.assertGreater(len(fomo_indicators), 0)
        self.assertEqual(len(normal_indicators), 0)
    
    def test_analyze_tweet(self):
        """測試完整推文分析"""
        mock_tweet = {
            'text': 'Uniswap V3 is revolutionary! 🚀 #DeFi #Uniswap $UNI to the moon!',
            'author': {
                'username': 'defi_expert',
                'verified': True,
                'followers_count': 50000
            },
            'metrics': {
                'like_count': 500,
                'retweet_count': 200,
                'reply_count': 100,
                'quote_count': 50
            }
        }
        
        result = self.analyzer.analyze_tweet(mock_tweet)
        
        # 檢查基本分析結果
        self.assertIn('sentiment', result)
        self.assertIn('hashtags', result)
        self.assertIn('mentions', result)
        self.assertIn('cashtags', result)
        self.assertIn('influence_score', result)
        self.assertIn('weighted_sentiment', result)
        
        # 檢查具體內容
        self.assertIn('DeFi', result['hashtags'])
        self.assertIn('Uniswap', result['hashtags'])
        self.assertIn('UNI', result['cashtags'])
        self.assertGreater(result['sentiment']['score'], 0)  # 應該是正面情緒
        self.assertGreater(result['influence_score'], 1)  # 應該有影響力加成
    
    def test_generate_twitter_sentiment_report(self):
        """測試 Twitter 情緒報告生成"""
        mock_tweets = [
            {
                'text': 'Bullish on DeFi! 🚀 #DeFi',
                'author': {'username': 'user1', 'verified': False, 'followers_count': 1000},
                'metrics': {'like_count': 10, 'retweet_count': 5, 'reply_count': 2, 'quote_count': 1}
            },
            {
                'text': 'DeFi is risky, be careful 📉 #DeFi',
                'author': {'username': 'user2', 'verified': True, 'followers_count': 10000},
                'metrics': {'like_count': 50, 'retweet_count': 20, 'reply_count': 10, 'quote_count': 5}
            },
            {
                'text': 'Just sharing DeFi news #DeFi',
                'author': {'username': 'user3', 'verified': False, 'followers_count': 500},
                'metrics': {'like_count': 5, 'retweet_count': 1, 'reply_count': 1, 'quote_count': 0}
            }
        ]
        
        report = self.analyzer.generate_twitter_sentiment_report(mock_tweets)
        
        # 檢查報告結構
        self.assertIn('summary', report)
        self.assertIn('sentiment_distribution', report)
        self.assertIn('trending_topics', report)
        self.assertIn('influence_analysis', report)
        self.assertIn('market_signals', report)
        
        # 檢查摘要數據
        self.assertEqual(report['summary']['total_tweets'], 3)
        self.assertIsInstance(report['summary']['avg_sentiment'], float)
        
        # 檢查情緒分布
        sentiment_dist = report['sentiment_distribution']
        self.assertEqual(sentiment_dist['positive'] + sentiment_dist['negative'] + sentiment_dist['neutral'], 3)


if __name__ == '__main__':
    unittest.main()
