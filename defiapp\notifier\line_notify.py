"""
LINE Notify 通知器
"""
import requests
import logging
from typing import Dict, Any, Optional
from defiapp.config import LINE_NOTIFY_TOKEN

logger = logging.getLogger(__name__)


class LineNotifier:
    """LINE Notify 通知器"""
    
    def __init__(self, token: str = None):
        self.token = token or LINE_NOTIFY_TOKEN
        self.api_url = "https://notify-api.line.me/api/notify"
    
    def send_message(self, message: str) -> bool:
        """發送訊息"""
        if not self.token:
            logger.warning("LINE Notify token 未設置")
            return False
        
        try:
            headers = {
                'Authorization': f'Bearer {self.token}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {'message': message}
            
            response = requests.post(self.api_url, headers=headers, data=data)
            response.raise_for_status()
            
            logger.info("LINE Notify 訊息發送成功")
            return True
            
        except Exception as e:
            logger.error(f"發送 LINE Notify 訊息失敗: {e}")
            return False
    
    def send_investment_alert(self, analysis_result: Dict[str, Any]) -> bool:
        """發送投資提醒"""
        try:
            project_name = analysis_result.get('project_name', 'Unknown')
            investment_score = analysis_result.get('investment_score', 0)
            recommendation = analysis_result.get('recommendation', 'Hold')
            predicted_return = analysis_result.get('predicted_return_7d', 0)
            risk_level = analysis_result.get('risk_level', 'Medium')
            
            # 根據投資分數選擇表情符號
            if investment_score > 0.5:
                emoji = "🚀"
            elif investment_score > 0.2:
                emoji = "📈"
            elif investment_score < -0.2:
                emoji = "📉"
            elif investment_score < -0.5:
                emoji = "⚠️"
            else:
                emoji = "📊"
            
            message = f"""
{emoji} DeFi 投資提醒

項目: {project_name}
投資分數: {investment_score:.3f}
建議: {recommendation}
預期7日回報: {predicted_return:+.2%}
風險等級: {risk_level}

時間: {analysis_result.get('analysis_date', 'N/A')}
            """.strip()
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"發送投資提醒失敗: {e}")
            return False
    
    def send_market_summary(self, market_summary: Dict[str, Any]) -> bool:
        """發送市場摘要"""
        try:
            total_protocols = market_summary.get('total_protocols', 0)
            total_tvl = market_summary.get('total_tvl', 0)
            avg_sentiment = market_summary.get('avg_sentiment', 0)
            trending_topics = market_summary.get('trending_topics', [])
            
            # 情緒表情符號
            if avg_sentiment > 0.1:
                sentiment_emoji = "😊"
            elif avg_sentiment < -0.1:
                sentiment_emoji = "😟"
            else:
                sentiment_emoji = "😐"
            
            message = f"""
📊 DeFi 市場日報

協議總數: {total_protocols}
總 TVL: ${total_tvl/1e9:.2f}B
市場情緒: {sentiment_emoji} {avg_sentiment:.3f}

熱門話題:
            """.strip()
            
            # 添加熱門話題
            if trending_topics:
                for i, (topic, count) in enumerate(trending_topics[:5], 1):
                    message += f"\n{i}. {topic} ({count})"
            else:
                message += "\n暫無熱門話題"
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"發送市場摘要失敗: {e}")
            return False


class NotificationManager:
    """通知管理器"""
    
    def __init__(self):
        self.telegram = TelegramNotifier()
        self.line = LineNotifier()
    
    def send_to_all(self, message_type: str, data: Dict[str, Any]) -> Dict[str, bool]:
        """發送到所有通知渠道"""
        results = {}
        
        if message_type == "investment_alert":
            results['telegram'] = self.telegram.send_investment_alert(data)
            results['line'] = self.line.send_investment_alert(data)
        elif message_type == "market_summary":
            results['telegram'] = self.telegram.send_market_summary(data)
            results['line'] = self.line.send_market_summary(data)
        elif message_type == "news_alert":
            results['telegram'] = self.telegram.send_news_alert(data)
            # LINE Notify 沒有專門的新聞提醒方法，使用通用訊息
            if hasattr(data, 'title'):
                message = f"📰 重要新聞: {data.get('title', '')}"
                results['line'] = self.line.send_message(message)
        
        return results
