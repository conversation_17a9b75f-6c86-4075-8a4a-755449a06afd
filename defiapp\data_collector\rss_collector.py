"""
RSS 新聞收集器
"""
import logging
import feedparser
from datetime import datetime
from typing import List, Dict, Any
from defiapp.config import RSS_FEEDS, MONITORED_KEYWORDS

logger = logging.getLogger(__name__)


class RSSCollector:
    """RSS 新聞收集器"""
    
    def __init__(self):
        self.feeds = RSS_FEEDS
        self.keywords = MONITORED_KEYWORDS
    
    def collect_news(self) -> List[Dict[str, Any]]:
        """收集所有 RSS 來源的新聞"""
        all_news = []
        
        for feed_url in self.feeds:
            try:
                news_items = self._parse_feed(feed_url)
                all_news.extend(news_items)
                logger.info(f"從 {feed_url} 收集到 {len(news_items)} 條新聞")
            except Exception as e:
                logger.error(f"收集 RSS {feed_url} 時發生錯誤: {e}")
        
        return all_news
    
    def _parse_feed(self, feed_url: str) -> List[Dict[str, Any]]:
        """解析單個 RSS feed"""
        feed = feedparser.parse(feed_url)
        news_items = []
        
        for entry in feed.entries:
            # 檢查是否包含關鍵字
            if self._contains_keywords(entry):
                news_item = {
                    'title': entry.get('title', ''),
                    'link': entry.get('link', ''),
                    'description': entry.get('description', ''),
                    'published': self._parse_date(entry.get('published', '')),
                    'source': feed_url,
                    'source_name': feed.feed.get('title', 'Unknown'),
                    'content': entry.get('content', [{}])[0].get('value', '') if entry.get('content') else '',
                    'tags': [tag.term for tag in entry.get('tags', [])],
                    'collected_at': datetime.now()
                }
                news_items.append(news_item)
        
        return news_items
    
    def _contains_keywords(self, entry) -> bool:
        """檢查新聞是否包含監控的關鍵字"""
        text_to_check = f"{entry.get('title', '')} {entry.get('description', '')}"
        text_to_check = text_to_check.lower()
        
        return any(keyword.lower() in text_to_check for keyword in self.keywords)
    
    def _parse_date(self, date_str: str) -> datetime:
        """解析日期字符串"""
        try:
            # 嘗試解析 RSS 日期格式
            parsed_date = feedparser._parse_date(date_str)
            if parsed_date:
                return datetime(*parsed_date[:6])
        except:
            pass
        
        return datetime.now()
