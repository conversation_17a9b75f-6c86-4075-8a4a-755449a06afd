# Twitter Cookie 認證設置指南

## 🍪 為什麼使用 Cookie 認證？

使用瀏覽器 Cookie 認證比官方 API 有以下優勢：
- **免費使用**: 不需要申請 Twitter Developer 帳號
- **更高限制**: 比免費 API 有更高的請求限制
- **即時設置**: 只需要從瀏覽器複製 cookie 即可使用
- **穩定性**: 不會因為 API 政策變化而受影響

## 📋 設置步驟

### 1. 登錄 Twitter
在瀏覽器中登錄您的 Twitter 帳號 (https://twitter.com)

### 2. 打開開發者工具
- **Chrome/Edge**: 按 `F12` 或右鍵選擇 "檢查"
- **Firefox**: 按 `F12` 或右鍵選擇 "檢查元素"

### 3. 獲取 Cookie 信息

#### 方法 A: 從 Network 標籤獲取
1. 點擊 "Network" (網路) 標籤
2. 刷新 Twitter 頁面 (`F5`)
3. 在請求列表中找到任何對 `twitter.com` 的請求
4. 點擊該請求，查看 "Request Headers"
5. 找到 `Cookie:` 行，複製整個 cookie 字符串

#### 方法 B: 從 Application 標籤獲取 (推薦)
1. 點擊 "Application" (應用程式) 標籤
2. 在左側展開 "Cookies" → "https://twitter.com"
3. 找到並複製以下兩個值：
   - `auth_token`: 通常是 40 個字符的十六進制字符串
   - `ct0`: 通常是 32 個字符的十六進制字符串

### 4. 配置系統

#### 選項 1: 修改配置文件 (推薦)
編輯 `defiapp/config.py` 文件：

```python
# 將您的 cookie 信息填入這裡
TWITTER_COOKIE_STRING = "auth_token=您的auth_token;ct0=您的ct0"
```

#### 選項 2: 設置環境變數
```bash
# Windows PowerShell
$env:TWITTER_AUTH_TOKEN="您的auth_token"
$env:TWITTER_CT0="您的ct0"

# Windows CMD
set TWITTER_AUTH_TOKEN=您的auth_token
set TWITTER_CT0=您的ct0

# Linux/Mac
export TWITTER_AUTH_TOKEN="您的auth_token"
export TWITTER_CT0="您的ct0"
```

#### 選項 3: 使用 .env 文件
創建 `.env` 文件：
```
TWITTER_AUTH_TOKEN=您的auth_token
TWITTER_CT0=您的ct0
```

## 🔍 Cookie 格式示例

### 完整 Cookie 字符串格式:
```
auth_token=1234567890abcdef1234567890abcdef12345678; ct0=abcdef1234567890abcdef1234567890; personalization_id=...; guest_id=...
```

### 提取的關鍵信息:
- **auth_token**: `1234567890abcdef1234567890abcdef12345678`
- **ct0**: `abcdef1234567890abcdef1234567890`

## ✅ 測試連接

### 1. 使用 Web 介面測試
1. 啟動系統: `poetry run python run.py run`
2. 訪問測試頁面: http://localhost:5000/test
3. 點擊 "測試Twitter API" 按鈕
4. 查看返回結果

### 2. 使用 API 端點測試
直接訪問: http://localhost:5000/api/test-twitter

### 3. 使用命令行測試
```bash
poetry run python -c "
from defiapp.data_collector.twitter_collector import TwitterCollector
from defiapp.config import TWITTER_AUTH_TOKEN, TWITTER_CT0

collector = TwitterCollector(auth_token=TWITTER_AUTH_TOKEN, ct0=TWITTER_CT0)
result = collector.test_api_connection()
print(result)
"
```

## 🔧 故障排除

### 問題 1: 401 Unauthorized
**原因**: Cookie 已過期或格式不正確
**解決方案**: 
- 重新登錄 Twitter 並獲取新的 cookie
- 檢查 auth_token 和 ct0 格式是否正確

### 問題 2: 403 Forbidden
**原因**: 請求頻率過高或帳號被限制
**解決方案**:
- 降低請求頻率
- 等待一段時間後重試
- 檢查帳號是否正常

### 問題 3: Cookie 格式錯誤
**檢查清單**:
- auth_token 應該是 40 個字符的十六進制字符串
- ct0 應該是 32 個字符的十六進制字符串
- 確保沒有包含額外的空格或特殊字符

### 問題 4: 無法獲取數據
**可能原因**:
- 網路連接問題
- Twitter 服務器問題
- Cookie 權限不足

## 🔒 安全注意事項

### ⚠️ 重要警告
- **不要分享您的 auth_token 和 ct0**: 這些信息可以用來訪問您的 Twitter 帳號
- **定期更新**: Cookie 會定期過期，需要重新獲取
- **使用專用帳號**: 建議使用專門的帳號進行數據收集
- **遵守使用條款**: 確保您的使用符合 Twitter 的服務條款

### 🛡️ 最佳實踐
1. **使用環境變數**: 不要將認證信息硬編碼在代碼中
2. **定期檢查**: 定期檢查 cookie 是否仍然有效
3. **監控使用**: 監控 API 使用情況，避免過度請求
4. **備份方案**: 準備多個認證方式作為備份

## 📊 使用統計

設置完成後，您可以在系統中看到：
- Twitter 數據收集狀態
- 認證方式 (Cookie 認證)
- 收集到的推文數量
- 情緒分析結果

## 🚀 高級功能

### 自動 Cookie 更新
您可以編寫腳本定期更新 cookie：

```python
from defiapp.utils.twitter_auth import extract_twitter_cookies_from_browser_string

# 從瀏覽器獲取新的 cookie 字符串
new_cookie_string = "..."
auth_token, ct0 = extract_twitter_cookies_from_browser_string(new_cookie_string)

# 更新配置
if auth_token and ct0:
    # 更新配置文件或環境變數
    print(f"新的 auth_token: {auth_token}")
    print(f"新的 ct0: {ct0}")
```

### 多帳號支持
您可以配置多個 Twitter 帳號進行數據收集：

```python
# 在配置文件中設置多個帳號
TWITTER_ACCOUNTS = [
    {"auth_token": "account1_token", "ct0": "account1_ct0"},
    {"auth_token": "account2_token", "ct0": "account2_ct0"}
]
```

## 📞 支援

如果您在設置過程中遇到問題：
1. 檢查本文檔的故障排除部分
2. 查看系統日誌文件 (`logs/defiapp.log`)
3. 在 GitHub Issues 中報告問題
4. 聯繫技術支援
