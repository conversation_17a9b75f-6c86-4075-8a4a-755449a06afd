"""
Twitter 專用分析器
"""
import re
import logging
from typing import Dict, List, Any, Tuple
from collections import Counter
from datetime import datetime, timedelta
from .nlp_analyzer import NLPAnalyzer

logger = logging.getLogger(__name__)


class TwitterAnalyzer(NLPAnalyzer):
    """Twitter 專用分析器"""
    
    def __init__(self):
        super().__init__()
        
        # Twitter 特有的正面詞彙
        self.twitter_positive_words = {
            'moon', 'mooning', 'bullish', 'pump', 'pumping', 'gem', 'diamond',
            'hands', 'hodl', 'ape', 'aping', 'rocket', 'lambo', 'wagmi',
            'gm', 'gn', 'lfg', 'based', 'chad', 'alpha', 'degen', 'anon'
        }
        
        # Twitter 特有的負面詞彙
        self.twitter_negative_words = {
            'dump', 'dumping', 'rekt', 'ngmi', 'fud', 'bear', 'bearish',
            'rug', 'rugged', 'scam', 'ponzi', 'exit', 'dead', 'rip'
        }
        
        # 更新詞彙集合
        self.positive_words.update(self.twitter_positive_words)
        self.negative_words.update(self.twitter_negative_words)
        
        # 影響力權重
        self.influence_weights = {
            'verified': 2.0,
            'high_followers': 1.5,  # >10k followers
            'medium_followers': 1.2,  # 1k-10k followers
            'engagement_ratio': 1.3  # high engagement ratio
        }
    
    def analyze_tweet(self, tweet: Dict[str, Any]) -> Dict[str, Any]:
        """分析單條推文"""
        text = tweet.get('text', '')
        author = tweet.get('author', {})
        metrics = tweet.get('metrics', {})
        
        # 基礎 NLP 分析
        base_analysis = self.analyze_text(text)
        
        # Twitter 特有分析
        twitter_analysis = {
            'hashtags': self._extract_hashtags(text),
            'mentions': self._extract_mentions(text),
            'urls': self._extract_urls(text),
            'cashtags': self._extract_cashtags(text),
            'emoji_sentiment': self._analyze_emoji_sentiment(text),
            'influence_score': self._calculate_influence_score(author, metrics),
            'virality_potential': self._calculate_virality_potential(metrics, text),
            'fomo_indicators': self._detect_fomo_indicators(text),
            'technical_signals': self._extract_technical_signals(text)
        }
        
        # 合併分析結果
        combined_analysis = {**base_analysis, **twitter_analysis}
        
        # 調整情緒分數（考慮影響力）
        influence_multiplier = min(2.0, 1.0 + twitter_analysis['influence_score'] * 0.1)
        combined_analysis['weighted_sentiment'] = base_analysis['sentiment']['score'] * influence_multiplier
        
        return combined_analysis
    
    def analyze_tweet_batch(self, tweets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量分析推文"""
        analyzed_tweets = []
        
        for tweet in tweets:
            try:
                analysis = self.analyze_tweet(tweet)
                tweet_with_analysis = tweet.copy()
                tweet_with_analysis['analysis'] = analysis
                analyzed_tweets.append(tweet_with_analysis)
            except Exception as e:
                logger.error(f"分析推文失敗: {e}")
                continue
        
        return analyzed_tweets
    
    def generate_twitter_sentiment_report(self, tweets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成 Twitter 情緒報告"""
        if not tweets:
            return {}
        
        analyzed_tweets = self.analyze_tweet_batch(tweets)
        
        # 計算整體指標
        total_tweets = len(analyzed_tweets)
        sentiment_scores = [t['analysis']['sentiment']['score'] for t in analyzed_tweets]
        weighted_scores = [t['analysis']['weighted_sentiment'] for t in analyzed_tweets]
        influence_scores = [t['analysis']['influence_score'] for t in analyzed_tweets]
        
        # 統計情緒分布
        positive_count = sum(1 for s in sentiment_scores if s > 0.1)
        negative_count = sum(1 for s in sentiment_scores if s < -0.1)
        neutral_count = total_tweets - positive_count - negative_count
        
        # 熱門話題
        all_hashtags = []
        all_mentions = []
        all_cashtags = []
        
        for tweet in analyzed_tweets:
            analysis = tweet['analysis']
            all_hashtags.extend(analysis.get('hashtags', []))
            all_mentions.extend(analysis.get('mentions', []))
            all_cashtags.extend(analysis.get('cashtags', []))
        
        # 影響力分析
        high_influence_tweets = [t for t in analyzed_tweets if t['analysis']['influence_score'] > 5]
        viral_potential_tweets = [t for t in analyzed_tweets if t['analysis']['virality_potential'] > 0.7]
        
        report = {
            'summary': {
                'total_tweets': total_tweets,
                'avg_sentiment': sum(sentiment_scores) / total_tweets if total_tweets > 0 else 0,
                'weighted_avg_sentiment': sum(weighted_scores) / total_tweets if total_tweets > 0 else 0,
                'avg_influence': sum(influence_scores) / total_tweets if total_tweets > 0 else 0
            },
            'sentiment_distribution': {
                'positive': positive_count,
                'negative': negative_count,
                'neutral': neutral_count,
                'positive_ratio': positive_count / total_tweets if total_tweets > 0 else 0
            },
            'trending_topics': {
                'hashtags': Counter(all_hashtags).most_common(10),
                'mentions': Counter(all_mentions).most_common(10),
                'cashtags': Counter(all_cashtags).most_common(10)
            },
            'influence_analysis': {
                'high_influence_count': len(high_influence_tweets),
                'viral_potential_count': len(viral_potential_tweets),
                'top_influential_tweets': sorted(high_influence_tweets, 
                                               key=lambda x: x['analysis']['influence_score'], 
                                               reverse=True)[:5]
            },
            'market_signals': self._extract_market_signals(analyzed_tweets),
            'generated_at': datetime.now()
        }
        
        return report
    
    def _extract_hashtags(self, text: str) -> List[str]:
        """提取 hashtags"""
        return re.findall(r'#(\w+)', text)
    
    def _extract_mentions(self, text: str) -> List[str]:
        """提取 mentions"""
        return re.findall(r'@(\w+)', text)
    
    def _extract_urls(self, text: str) -> List[str]:
        """提取 URLs"""
        return re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text)
    
    def _extract_cashtags(self, text: str) -> List[str]:
        """提取 cashtags ($符號)"""
        return re.findall(r'\$([A-Z]{2,10})', text)
    
    def _analyze_emoji_sentiment(self, text: str) -> float:
        """分析表情符號情緒"""
        positive_emojis = ['🚀', '🌙', '💎', '🔥', '💪', '👍', '😍', '🤑', '💰', '📈']
        negative_emojis = ['📉', '💀', '😭', '😱', '🤮', '👎', '😡', '💸', '⚠️', '🚨']
        
        positive_count = sum(text.count(emoji) for emoji in positive_emojis)
        negative_count = sum(text.count(emoji) for emoji in negative_emojis)
        
        if positive_count + negative_count == 0:
            return 0.0
        
        return (positive_count - negative_count) / (positive_count + negative_count)
    
    def _calculate_influence_score(self, author: Dict[str, Any], metrics: Dict[str, Any]) -> float:
        """計算影響力分數"""
        score = 1.0
        
        # 驗證用戶加分
        if author.get('verified', False):
            score *= self.influence_weights['verified']
        
        # 粉絲數加分
        followers = author.get('followers_count', 0)
        if followers > 10000:
            score *= self.influence_weights['high_followers']
        elif followers > 1000:
            score *= self.influence_weights['medium_followers']
        
        # 互動率加分
        likes = metrics.get('like_count', 0)
        retweets = metrics.get('retweet_count', 0)
        replies = metrics.get('reply_count', 0)
        
        total_engagement = likes + retweets * 3 + replies * 2
        if followers > 0:
            engagement_rate = total_engagement / followers
            if engagement_rate > 0.05:  # 5% 以上互動率
                score *= self.influence_weights['engagement_ratio']
        
        return min(10.0, score)  # 限制最高分數
    
    def _calculate_virality_potential(self, metrics: Dict[str, Any], text: str) -> float:
        """計算病毒傳播潛力"""
        retweets = metrics.get('retweet_count', 0)
        likes = metrics.get('like_count', 0)
        replies = metrics.get('reply_count', 0)
        
        # 基於互動數的病毒潛力
        engagement_score = (retweets * 3 + likes + replies * 2) / 100
        
        # 基於內容的病毒潛力
        viral_keywords = ['breaking', 'alert', 'urgent', 'massive', 'huge', 'insane']
        content_score = sum(1 for keyword in viral_keywords if keyword.lower() in text.lower()) * 0.2
        
        return min(1.0, engagement_score + content_score)
    
    def _detect_fomo_indicators(self, text: str) -> List[str]:
        """檢測 FOMO 指標"""
        fomo_patterns = [
            r'(?i)\b(last chance|don\'t miss|limited time|act fast|hurry)\b',
            r'(?i)\b(moon|mooning|to the moon)\b',
            r'(?i)\b(ape|aping|yolo)\b',
            r'(?i)\b(gem|hidden gem|next big thing)\b',
            r'(?i)\b(100x|1000x|\d+x potential)\b'
        ]
        
        indicators = []
        for pattern in fomo_patterns:
            if re.search(pattern, text):
                indicators.append(pattern)
        
        return indicators
    
    def _extract_technical_signals(self, text: str) -> Dict[str, Any]:
        """提取技術分析信號"""
        signals = {
            'price_targets': re.findall(r'\$(\d+(?:\.\d+)?)', text),
            'percentage_moves': re.findall(r'(\d+(?:\.\d+)?)%', text),
            'support_resistance': [],
            'chart_patterns': []
        }
        
        # 檢測支撐阻力位
        support_resistance_patterns = [
            r'(?i)\b(support|resistance)\s+(?:at\s+)?\$?(\d+(?:\.\d+)?)',
            r'(?i)\b(break(?:out|down))\s+(?:above|below)\s+\$?(\d+(?:\.\d+)?)'
        ]
        
        for pattern in support_resistance_patterns:
            matches = re.findall(pattern, text)
            signals['support_resistance'].extend(matches)
        
        # 檢測圖表形態
        chart_patterns = ['triangle', 'wedge', 'flag', 'pennant', 'head and shoulders', 'cup and handle']
        for pattern in chart_patterns:
            if pattern.lower() in text.lower():
                signals['chart_patterns'].append(pattern)
        
        return signals
    
    def _extract_market_signals(self, analyzed_tweets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """提取市場信號"""
        signals = {
            'bullish_signals': 0,
            'bearish_signals': 0,
            'fomo_level': 0,
            'fear_level': 0,
            'trending_tokens': [],
            'key_events': []
        }
        
        for tweet in analyzed_tweets:
            analysis = tweet['analysis']
            sentiment = analysis['sentiment']['score']
            
            if sentiment > 0.3:
                signals['bullish_signals'] += 1
            elif sentiment < -0.3:
                signals['bearish_signals'] += 1
            
            if analysis.get('fomo_indicators'):
                signals['fomo_level'] += len(analysis['fomo_indicators'])
            
            # 收集提及的代幣
            cashtags = analysis.get('cashtags', [])
            signals['trending_tokens'].extend(cashtags)
        
        # 統計最熱門的代幣
        if signals['trending_tokens']:
            token_counts = Counter(signals['trending_tokens'])
            signals['trending_tokens'] = token_counts.most_common(10)
        
        return signals
