<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeFi 自動化投研系統</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .metric-card {
            border-left: 4px solid #667eea;
        }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .neutral { color: #6c757d; }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-xl navbar-dark bg-dark">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="#">🚀 DeFi 投研系統</a>
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarNav">
                            <div class="navbar-nav ms-auto">
                                <button class="btn btn-outline-light me-2 mb-2 mb-lg-0" onclick="collectData()">收集數據</button>
                                <button class="btn btn-outline-light me-2 mb-2 mb-lg-0" onclick="analyzeNews()">分析新聞</button>
                                <button class="btn btn-outline-light mb-2 mb-lg-0" onclick="collectTwitter()">收集Twitter</button>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <div class="row mt-4">
            <!-- 市場摘要 -->
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <h6 class="card-title">總協議數</h6>
                        <h3 id="total-protocols">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <h6 class="card-title">總 TVL</h6>
                        <h3 id="total-tvl">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <h6 class="card-title">平均情緒</h6>
                        <h3 id="avg-sentiment">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <h6 class="card-title">熱門話題</h6>
                        <div id="trending-topics">-</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <!-- 最新新聞 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📰 最新新聞</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="recent-news">
                            <div class="loading">載入中...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TVL 排行 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 TVL 排行榜</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="tvl-ranking">
                            <div class="loading">載入中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <!-- 情緒分析 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>😊 新聞情緒分析</h5>
                    </div>
                    <div class="card-body">
                        <div id="sentiment-chart"></div>
                    </div>
                </div>
            </div>

            <!-- Twitter 情緒分析 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>🐦 Twitter 情緒</h5>
                    </div>
                    <div class="card-body">
                        <div id="twitter-sentiment-chart"></div>
                        <div class="mt-3">
                            <small class="text-muted">平均情緒: <span id="twitter-avg-sentiment">-</span></small><br>
                            <small class="text-muted">總推文數: <span id="twitter-total-tweets">-</span></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 價格圖表 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>💹 價格趨勢</h5>
                        <select id="coin-selector" class="form-select mt-2" onchange="loadPriceChart()">
                            <option value="ethereum">Ethereum</option>
                            <option value="uniswap">Uniswap</option>
                            <option value="aave">Aave</option>
                            <option value="compound-governance-token">Compound</option>
                        </select>
                    </div>
                    <div class="card-body">
                        <div id="price-chart"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <!-- Twitter 熱門推文 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🔥 Twitter 熱門推文</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="twitter-hot-tweets">
                            <div class="loading">載入中...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Twitter 趨勢話題 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Twitter 趨勢話題</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="twitter-trends">
                            <div class="loading">載入中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 載入儀表板數據
        function loadDashboard() {
            $('.loading').show();
            
            $.get('/api/dashboard')
                .done(function(response) {
                    if (response.success) {
                        updateMetrics(response.data.market_summary);
                        updateNews(response.data.recent_news);
                        updateTVLRanking(response.data.top_protocols);
                    }
                })
                .fail(function() {
                    alert('載入數據失敗');
                })
                .always(function() {
                    $('.loading').hide();
                });
        }

        // 更新指標
        function updateMetrics(summary) {
            $('#total-protocols').text(summary.total_protocols || 0);
            $('#total-tvl').text('$' + (summary.total_tvl / 1e9).toFixed(2) + 'B');
            
            const sentiment = summary.avg_sentiment || 0;
            const sentimentClass = sentiment > 0.1 ? 'positive' : sentiment < -0.1 ? 'negative' : 'neutral';
            $('#avg-sentiment').html(`<span class="${sentimentClass}">${sentiment.toFixed(3)}</span>`);
            
            if (summary.trending_topics && summary.trending_topics.length > 0) {
                const topics = summary.trending_topics.slice(0, 3).map(t => t[0]).join(', ');
                $('#trending-topics').text(topics);
            }
        }

        // 更新新聞
        function updateNews(news) {
            const newsHtml = news.map(item => `
                <div class="mb-3 p-2 border-bottom">
                    <h6><a href="${item.link}" target="_blank">${item.title}</a></h6>
                    <small class="text-muted">${item.source_name} - ${new Date(item.published_at).toLocaleDateString()}</small>
                    ${item.sentiment_score ? `<span class="badge bg-${item.sentiment_score > 0.1 ? 'success' : item.sentiment_score < -0.1 ? 'danger' : 'secondary'} ms-2">${item.sentiment_score.toFixed(2)}</span>` : ''}
                </div>
            `).join('');
            
            $('#recent-news').html(newsHtml || '<p>暫無新聞數據</p>');
        }

        // 更新 TVL 排行
        function updateTVLRanking(protocols) {
            const rankingHtml = protocols.map((protocol, index) => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 ${index < 3 ? 'bg-light' : ''}">
                    <div>
                        <strong>${protocol.protocol_name}</strong>
                        <small class="text-muted d-block">${protocol.category}</small>
                    </div>
                    <div class="text-end">
                        <div>$${(protocol.max_tvl / 1e9).toFixed(2)}B</div>
                        <small class="${protocol.avg_change_7d > 0 ? 'positive' : 'negative'}">${protocol.avg_change_7d > 0 ? '+' : ''}${protocol.avg_change_7d.toFixed(2)}%</small>
                    </div>
                </div>
            `).join('');
            
            $('#tvl-ranking').html(rankingHtml || '<p>暫無 TVL 數據</p>');
        }

        // 收集數據
        function collectData() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '收集中...';
            
            $.get('/api/collect-data')
                .done(function(response) {
                    alert(response.message);
                    if (response.success) {
                        loadDashboard();
                    }
                })
                .fail(function() {
                    alert('數據收集失敗');
                })
                .always(function() {
                    btn.disabled = false;
                    btn.textContent = '收集數據';
                });
        }

        // 分析新聞
        function analyzeNews() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '分析中...';

            $.get('/api/analyze-news')
                .done(function(response) {
                    alert(response.message);
                    if (response.success) {
                        loadDashboard();
                        loadSentimentAnalysis();
                    }
                })
                .fail(function() {
                    alert('新聞分析失敗');
                })
                .always(function() {
                    btn.disabled = false;
                    btn.textContent = '分析新聞';
                });
        }

        // 收集 Twitter 數據
        function collectTwitter() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '收集中...';

            $.get('/api/collect-twitter')
                .done(function(response) {
                    alert(response.message);
                    if (response.success) {
                        loadTwitterSentiment();
                        loadTwitterTrends();
                    }
                })
                .fail(function() {
                    alert('Twitter 數據收集失敗');
                })
                .always(function() {
                    btn.disabled = false;
                    btn.textContent = '收集Twitter';
                });
        }

        // 載入情緒分析
        function loadSentimentAnalysis() {
            $.get('/api/sentiment-analysis')
                .done(function(response) {
                    if (response.success) {
                        const data = response.data.sentiment_distribution;
                        const chartData = [{
                            values: [data.positive, data.negative, data.neutral],
                            labels: ['正面', '負面', '中性'],
                            type: 'pie',
                            marker: {
                                colors: ['#28a745', '#dc3545', '#6c757d']
                            }
                        }];
                        
                        Plotly.newPlot('sentiment-chart', chartData, {
                            title: '情緒分布',
                            height: 300
                        });
                    }
                });
        }

        // 載入價格圖表
        function loadPriceChart() {
            const coinId = $('#coin-selector').val();
            
            $.get(`/api/price-chart/${coinId}`)
                .done(function(response) {
                    if (response.success) {
                        const chartData = JSON.parse(response.chart);
                        Plotly.newPlot('price-chart', chartData.data, chartData.layout);
                    }
                });
        }

        // 載入 Twitter 情緒分析
        function loadTwitterSentiment() {
            $.get('/api/twitter-sentiment')
                .done(function(response) {
                    if (response.success) {
                        const summary = response.data.summary;
                        const tweets = response.data.high_engagement_tweets;

                        // 更新統計數據
                        $('#twitter-avg-sentiment').text(summary.avg_sentiment ? summary.avg_sentiment.toFixed(3) : '0.000');
                        $('#twitter-total-tweets').text(summary.total_tweets || 0);

                        // 更新情緒圖表
                        if (summary.total_tweets > 0) {
                            const chartData = [{
                                values: [summary.positive_count, summary.negative_count,
                                        summary.total_tweets - summary.positive_count - summary.negative_count],
                                labels: ['正面', '負面', '中性'],
                                type: 'pie',
                                marker: {
                                    colors: ['#28a745', '#dc3545', '#6c757d']
                                }
                            }];

                            Plotly.newPlot('twitter-sentiment-chart', chartData, {
                                title: 'Twitter 情緒分布',
                                height: 250
                            });
                        }

                        // 更新熱門推文
                        updateTwitterHotTweets(tweets);
                    }
                });
        }

        // 載入 Twitter 趨勢
        function loadTwitterTrends() {
            $.get('/api/twitter-trends')
                .done(function(response) {
                    if (response.success) {
                        updateTwitterTrends(response.data);
                    }
                });
        }

        // 更新熱門推文
        function updateTwitterHotTweets(tweets) {
            const tweetsHtml = tweets.map(tweet => `
                <div class="mb-3 p-2 border-bottom">
                    <div class="d-flex justify-content-between">
                        <strong>@${tweet.author_username}</strong>
                        <small class="text-muted">${new Date(tweet.created_at).toLocaleDateString()}</small>
                    </div>
                    <p class="mt-2">${tweet.text.substring(0, 200)}${tweet.text.length > 200 ? '...' : ''}</p>
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">
                            ❤️ ${tweet.like_count} 🔄 ${tweet.retweet_count} 💬 ${tweet.reply_count}
                        </small>
                        <span class="badge bg-${tweet.sentiment_score > 0.1 ? 'success' : tweet.sentiment_score < -0.1 ? 'danger' : 'secondary'}">
                            ${tweet.sentiment_score ? tweet.sentiment_score.toFixed(2) : '0.00'}
                        </span>
                    </div>
                </div>
            `).join('');

            $('#twitter-hot-tweets').html(tweetsHtml || '<p>暫無 Twitter 數據</p>');
        }

        // 更新 Twitter 趨勢
        function updateTwitterTrends(trendData) {
            let trendsHtml = '<h6>熱門 Hashtags</h6>';

            if (trendData.trending_topics && trendData.trending_topics.hashtags) {
                trendsHtml += '<ul class="list-unstyled">';
                trendData.trending_topics.hashtags.slice(0, 10).forEach(([tag, count]) => {
                    trendsHtml += `<li class="mb-1">#${tag} <span class="badge bg-primary">${count}</span></li>`;
                });
                trendsHtml += '</ul>';
            }

            if (trendData.market_signals) {
                const signals = trendData.market_signals;
                trendsHtml += '<h6 class="mt-3">市場信號</h6>';
                trendsHtml += `<p>看漲信號: <span class="text-success">${signals.bullish_signals}</span></p>`;
                trendsHtml += `<p>看跌信號: <span class="text-danger">${signals.bearish_signals}</span></p>`;
                trendsHtml += `<p>FOMO 指數: <span class="text-warning">${signals.fomo_level}</span></p>`;
            }

            $('#twitter-trends').html(trendsHtml);
        }

        // 頁面載入時執行
        $(document).ready(function() {
            loadDashboard();
            loadSentimentAnalysis();
            loadPriceChart();
            loadTwitterSentiment();
            loadTwitterTrends();
        });
    </script>
</body>
</html>
