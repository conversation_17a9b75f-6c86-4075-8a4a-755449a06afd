"""
Telegram Bot 通知器
"""
import requests
import logging
from typing import Dict, List, Any, Optional
from defiapp.config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID

logger = logging.getLogger(__name__)


class TelegramNotifier:
    """Telegram 通知器"""
    
    def __init__(self, bot_token: str = None, chat_id: str = None):
        self.bot_token = bot_token or TELEGRAM_BOT_TOKEN
        self.chat_id = chat_id or TELEGRAM_CHAT_ID
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
    
    def send_message(self, message: str, parse_mode: str = "HTML") -> bool:
        """發送訊息"""
        if not self.bot_token or not self.chat_id:
            logger.warning("Telegram Bot token 或 chat_id 未設置")
            return False
        
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode
            }
            
            response = requests.post(url, data=data)
            response.raise_for_status()
            
            logger.info("Telegram 訊息發送成功")
            return True
            
        except Exception as e:
            logger.error(f"發送 Telegram 訊息失敗: {e}")
            return False
    
    def send_investment_alert(self, analysis_result: Dict[str, Any]) -> bool:
        """發送投資提醒"""
        try:
            project_name = analysis_result.get('project_name', 'Unknown')
            investment_score = analysis_result.get('investment_score', 0)
            recommendation = analysis_result.get('recommendation', 'Hold')
            predicted_return = analysis_result.get('predicted_return_7d', 0)
            risk_level = analysis_result.get('risk_level', 'Medium')
            
            # 根據投資分數選擇表情符號
            if investment_score > 0.5:
                emoji = "🚀"
            elif investment_score > 0.2:
                emoji = "📈"
            elif investment_score < -0.2:
                emoji = "📉"
            elif investment_score < -0.5:
                emoji = "⚠️"
            else:
                emoji = "📊"
            
            message = f"""
{emoji} <b>DeFi 投資提醒</b>

<b>項目:</b> {project_name}
<b>投資分數:</b> {investment_score:.3f}
<b>建議:</b> {recommendation}
<b>預期7日回報:</b> {predicted_return:+.2%}
<b>風險等級:</b> {risk_level}

<i>時間: {analysis_result.get('analysis_date', 'N/A')}</i>
            """.strip()
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"發送投資提醒失敗: {e}")
            return False
    
    def send_market_summary(self, market_summary: Dict[str, Any]) -> bool:
        """發送市場摘要"""
        try:
            total_protocols = market_summary.get('total_protocols', 0)
            total_tvl = market_summary.get('total_tvl', 0)
            avg_sentiment = market_summary.get('avg_sentiment', 0)
            trending_topics = market_summary.get('trending_topics', [])
            
            # 情緒表情符號
            if avg_sentiment > 0.1:
                sentiment_emoji = "😊"
            elif avg_sentiment < -0.1:
                sentiment_emoji = "😟"
            else:
                sentiment_emoji = "😐"
            
            message = f"""
📊 <b>DeFi 市場日報</b>

<b>協議總數:</b> {total_protocols}
<b>總 TVL:</b> ${total_tvl/1e9:.2f}B
<b>市場情緒:</b> {sentiment_emoji} {avg_sentiment:.3f}

<b>熱門話題:</b>
            """.strip()
            
            # 添加熱門話題
            if trending_topics:
                for i, (topic, count) in enumerate(trending_topics[:5], 1):
                    message += f"\n{i}. {topic} ({count})"
            else:
                message += "\n暫無熱門話題"
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"發送市場摘要失敗: {e}")
            return False
    
    def send_news_alert(self, news_item: Dict[str, Any]) -> bool:
        """發送重要新聞提醒"""
        try:
            title = news_item.get('title', 'No Title')
            link = news_item.get('link', '')
            sentiment_score = news_item.get('sentiment_score', 0)
            mentioned_projects = news_item.get('mentioned_projects', [])
            
            # 根據情緒分數選擇表情符號
            if sentiment_score > 0.3:
                emoji = "🔥"
            elif sentiment_score > 0.1:
                emoji = "📰"
            elif sentiment_score < -0.3:
                emoji = "🚨"
            elif sentiment_score < -0.1:
                emoji = "⚠️"
            else:
                emoji = "ℹ️"
            
            message = f"""
{emoji} <b>重要新聞提醒</b>

<b>標題:</b> {title}

<b>情緒分數:</b> {sentiment_score:.3f}
            """.strip()
            
            if mentioned_projects:
                message += f"\n<b>相關項目:</b> {', '.join(mentioned_projects[:5])}"
            
            if link:
                message += f"\n\n<a href='{link}'>閱讀全文</a>"
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"發送新聞提醒失敗: {e}")
            return False
