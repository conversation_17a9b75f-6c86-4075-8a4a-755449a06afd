[tool.poetry]
name = "defiapp"
version = "0.1.0"
description = ""
authors = ["MikeLinBot <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.8"
requests = ">=2.25.0"
beautifulsoup4 = ">=4.9.0"
pandas = ">=1.3.0"
numpy = ">=1.21.0"
scikit-learn = ">=1.0.0,<1.3.0"
feedparser = "^6.0.11"
apscheduler = "^3.11.0"
flask = ">=2.0.0,<3.0.0"
plotly = ">=5.0.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
