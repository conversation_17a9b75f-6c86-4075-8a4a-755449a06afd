<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>測試頁面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>DeFi 投研系統測試頁面</h1>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary" onclick="collectData()">收集數據</button>
                    <button type="button" class="btn btn-success" onclick="analyzeNews()">分析新聞</button>
                    <button type="button" class="btn btn-info" onclick="collectTwitter()">收集Twitter</button>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div id="result" class="alert alert-info">
                    點擊按鈕測試功能...
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function collectData() {
            $('#result').html('正在收集數據...');
            $.get('/api/collect-data')
                .done(function(response) {
                    $('#result').html('<strong>成功:</strong> ' + response.message);
                })
                .fail(function() {
                    $('#result').html('<strong>失敗:</strong> 數據收集失敗');
                });
        }

        function analyzeNews() {
            $('#result').html('正在分析新聞...');
            $.get('/api/analyze-news')
                .done(function(response) {
                    $('#result').html('<strong>成功:</strong> ' + response.message);
                })
                .fail(function() {
                    $('#result').html('<strong>失敗:</strong> 新聞分析失敗');
                });
        }

        function collectTwitter() {
            $('#result').html('正在收集 Twitter 數據...');
            $.get('/api/collect-twitter')
                .done(function(response) {
                    $('#result').html('<strong>成功:</strong> ' + response.message);
                })
                .fail(function() {
                    $('#result').html('<strong>失敗:</strong> Twitter 數據收集失敗');
                });
        }
    </script>
</body>
</html>
