# Twitter 整合使用指南

## 🐦 概述

Twitter 模組是 DeFi 自動化投研系統的核心組件之一，專門用於收集和分析 Twitter 上的 DeFi 相關內容，包括：

- **KOL 觀點追蹤**: 監控重要 DeFi 項目創始人和意見領袖
- **市場情緒分析**: 實時分析 Twitter 上的市場情緒變化
- **趨勢話題發現**: 識別熱門 hashtags 和討論話題
- **影響力評估**: 根據用戶影響力加權分析內容重要性

## 🔧 配置設置

### 1. 獲取 Twitter API 憑證

1. 訪問 [Twitter Developer Portal](https://developer.twitter.com/)
2. 申請開發者帳號
3. 創建新的 App
4. 獲取 Bearer Token

### 2. 設置環境變數

```bash
# 設置 Twitter Bearer Token
export TWITTER_BEARER_TOKEN="your_bearer_token_here"
```

或在 `.env` 文件中設置：
```
TWITTER_BEARER_TOKEN=your_bearer_token_here
```

### 3. 配置監控帳號

在 `defiapp/config.py` 中自定義監控的 Twitter 帳號：

```python
TWITTER_VIP_ACCOUNTS = [
    'uniswap',           # Uniswap 官方
    'aaveaave',          # Aave 官方
    'compoundfinance',   # Compound 官方
    'makerdao',          # MakerDAO 官方
    'defillama',         # DefiLlama
    'haydenzadams',      # Uniswap 創始人
    'stani',             # Aave 創始人
    # 添加更多帳號...
]
```

## 📊 功能特色

### 1. 智能內容過濾
- 自動識別 DeFi 相關推文
- 過濾垃圾內容和無關信息
- 支持多語言內容分析

### 2. 深度情緒分析
- **基礎情緒**: 正面/負面/中性分類
- **表情符號分析**: 解析表情符號的情緒含義
- **FOMO 指標**: 檢測市場恐慌和貪婪情緒
- **炒作指數**: 量化內容的炒作程度

### 3. 影響力評估
- **驗證用戶加權**: 藍勾用戶獲得更高權重
- **粉絲數量評估**: 根據粉絲數量調整影響力
- **互動率分析**: 考慮推文的互動質量
- **病毒傳播潛力**: 預測內容的傳播可能性

### 4. 技術信號提取
- **價格目標**: 提取推文中的價格預測
- **技術分析**: 識別支撐阻力位、圖表形態
- **市場信號**: 統計看漲/看跌信號數量

## 🚀 使用方法

### 1. 手動收集數據

```bash
# 收集 Twitter 數據
poetry run python run.py collect

# 或者只收集 Twitter 數據
poetry run python -c "
from defiapp.data_collector.collector import DataCollector
collector = DataCollector()
tweets = collector.collect_twitter_data_only()
print(f'收集到 {len(tweets)} 條推文')
"
```

### 2. Web 介面操作

1. 啟動系統：`poetry run python run.py run`
2. 訪問 http://localhost:5000
3. 點擊 "收集Twitter" 按鈕
4. 查看 Twitter 情緒分析和熱門推文

### 3. API 端點

- `GET /api/collect-twitter`: 手動觸發 Twitter 數據收集
- `GET /api/twitter-sentiment`: 獲取 Twitter 情緒摘要
- `GET /api/twitter-trends`: 獲取 Twitter 趨勢分析

### 4. 程式化使用

```python
from defiapp.data_collector.twitter_collector import TwitterCollector
from defiapp.analyzer.twitter_analyzer import TwitterAnalyzer

# 初始化收集器和分析器
collector = TwitterCollector()
analyzer = TwitterAnalyzer()

# 收集特定用戶的推文
tweets = collector.collect_user_tweets('uniswap', max_results=50)

# 分析推文情緒
analyzed_tweets = analyzer.analyze_tweet_batch(tweets)

# 生成情緒報告
report = analyzer.generate_twitter_sentiment_report(analyzed_tweets)
print(f"平均情緒分數: {report['summary']['avg_sentiment']}")
```

## 📈 分析指標說明

### 1. 情緒分數 (Sentiment Score)
- **範圍**: -1.0 到 1.0
- **正值**: 表示正面情緒 (看漲)
- **負值**: 表示負面情緒 (看跌)
- **接近0**: 表示中性情緒

### 2. 影響力分數 (Influence Score)
- **範圍**: 1.0 到 10.0
- **計算因子**:
  - 驗證狀態 (2x 權重)
  - 粉絲數量 (1.2x-1.5x 權重)
  - 互動率 (1.3x 權重)

### 3. 互動分數 (Engagement Score)
- **計算公式**: 點讚數 × 1 + 轉推數 × 3 + 回覆數 × 2 + 引用數 × 2
- **用途**: 衡量推文的受歡迎程度

### 4. 病毒潛力 (Virality Potential)
- **範圍**: 0.0 到 1.0
- **因子**: 互動數量、病毒關鍵字、內容特徵

## ⚠️ 注意事項

### 1. API 限制
- Twitter API v2 有嚴格的速率限制
- 免費版本每月限制 500,000 推文
- 建議合理設置收集頻率

### 2. 數據質量
- 推文內容可能包含誤導信息
- 機器人帳號可能影響分析結果
- 建議結合其他數據源進行驗證

### 3. 合規使用
- 遵守 Twitter 服務條款
- 尊重用戶隱私
- 不要進行惡意爬取

## 🔧 故障排除

### 1. 無法收集數據
```bash
# 檢查 API 憑證
echo $TWITTER_BEARER_TOKEN

# 測試 API 連接
poetry run python -c "
from defiapp.data_collector.twitter_collector import TwitterCollector
collector = TwitterCollector()
status = collector.get_rate_limit_status()
print(status)
"
```

### 2. 情緒分析不準確
- 檢查關鍵字配置是否合適
- 調整情緒分析權重
- 增加訓練數據

### 3. 性能問題
- 減少監控帳號數量
- 增加收集間隔時間
- 優化資料庫查詢

## 📊 最佳實踐

### 1. 監控策略
- 重點關注項目官方帳號
- 平衡 KOL 和普通用戶內容
- 定期更新監控列表

### 2. 分析策略
- 結合多個時間維度分析
- 考慮市場背景和事件
- 交叉驗證不同數據源

### 3. 風險管理
- 不要完全依賴 Twitter 情緒
- 注意假消息和操縱行為
- 保持理性投資決策

## 🚀 未來擴展

### 計劃功能
- [ ] 實時推文流處理
- [ ] 更精確的機器學習模型
- [ ] 多語言情緒分析
- [ ] 圖片和視頻內容分析
- [ ] 網絡影響力分析
- [ ] 自動交易信號生成

### 技術改進
- [ ] 使用 Transformer 模型
- [ ] 實現增量學習
- [ ] 添加異常檢測
- [ ] 優化存儲結構
