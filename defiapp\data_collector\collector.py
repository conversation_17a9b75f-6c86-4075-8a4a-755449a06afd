"""
主要數據收集器
"""
import logging
from typing import List, Dict, Any
from .rss_collector import RSSCollector
from .api_collector import APICollector
from .twitter_collector import TwitterCollector

logger = logging.getLogger(__name__)


class DataCollector:
    """主要數據收集器"""
    
    def __init__(self):
        self.rss_collector = RSSCollector()
        self.api_collector = APICollector()
        self.twitter_collector = TwitterCollector()
    
    def collect_all_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """收集所有類型的數據"""
        logger.info("開始收集數據...")
        
        data = {
            'news': [],
            'prices': [],
            'tvl': [],
            'crypto_news': [],
            'twitter': []
        }
        
        try:
            # 收集 RSS 新聞
            data['news'] = self.rss_collector.collect_news()
            
            # 收集主要 DeFi 代幣價格數據
            defi_tokens = [
                'ethereum', 'uniswap', 'aave', 'compound-governance-token',
                'maker', 'curve-dao-token', 'yearn-finance', 'sushi',
                'pancakeswap-token', '1inch', 'the-graph'
            ]
            data['prices'] = self.api_collector.collect_coingecko_data(defi_tokens)
            
            # 收集 TVL 數據
            data['tvl'] = self.api_collector.collect_defillama_tvl()

            # 收集 Twitter 數據
            data['twitter'] = self.twitter_collector.collect_trending_defi_tweets()

            # 收集加密貨幣新聞（需要 API token）
            # data['crypto_news'] = self.api_collector.collect_crypto_news()

            logger.info(f"數據收集完成: 新聞 {len(data['news'])} 條, "
                       f"價格數據 {len(data['prices'])} 個, "
                       f"TVL 數據 {len(data['tvl'])} 個, "
                       f"Twitter 數據 {len(data['twitter'])} 條")
            
        except Exception as e:
            logger.error(f"數據收集過程中發生錯誤: {e}")
        
        return data
    
    def collect_news_only(self) -> List[Dict[str, Any]]:
        """只收集新聞數據"""
        return self.rss_collector.collect_news()
    
    def collect_market_data_only(self) -> Dict[str, List[Dict[str, Any]]]:
        """只收集市場數據"""
        defi_tokens = [
            'ethereum', 'uniswap', 'aave', 'compound-governance-token',
            'maker', 'curve-dao-token', 'yearn-finance', 'sushi'
        ]
        
        return {
            'prices': self.api_collector.collect_coingecko_data(defi_tokens),
            'tvl': self.api_collector.collect_defillama_tvl()
        }

    def collect_twitter_data_only(self) -> List[Dict[str, Any]]:
        """只收集 Twitter 數據"""
        return self.twitter_collector.collect_trending_defi_tweets()

    def collect_twitter_sentiment_analysis(self) -> Dict[str, Any]:
        """收集並分析 Twitter 情緒"""
        from defiapp.analyzer.twitter_analyzer import TwitterAnalyzer

        # 收集 Twitter 數據
        tweets = self.twitter_collector.collect_trending_defi_tweets()

        if not tweets:
            return {'error': 'No Twitter data collected'}

        # 分析 Twitter 情緒
        analyzer = TwitterAnalyzer()
        sentiment_report = analyzer.generate_twitter_sentiment_report(tweets)

        return {
            'tweets_collected': len(tweets),
            'sentiment_report': sentiment_report,
            'raw_tweets': tweets[:10]  # 返回前10條作為示例
        }
