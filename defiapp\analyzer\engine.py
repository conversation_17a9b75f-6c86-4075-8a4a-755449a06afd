"""
分析引擎
"""
import logging
from typing import Dict, List, Any
from .nlp_analyzer import NLPAnalyzer
from defiapp.config import INVESTMENT_SCORE_WEIGHTS

logger = logging.getLogger(__name__)


class AnalysisEngine:
    """分析引擎"""
    
    def __init__(self):
        self.nlp_analyzer = NLPAnalyzer()
        self.weights = INVESTMENT_SCORE_WEIGHTS
    
    def analyze_news_batch(self, news_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量分析新聞"""
        analyzed_news = []
        
        for news_item in news_items:
            try:
                analysis = self.analyze_single_news(news_item)
                analyzed_news.append(analysis)
            except Exception as e:
                logger.error(f"分析新聞時發生錯誤: {e}")
                continue
        
        return analyzed_news
    
    def analyze_single_news(self, news_item: Dict[str, Any]) -> Dict[str, Any]:
        """分析單條新聞"""
        # 合併標題和描述進行分析
        text_to_analyze = f"{news_item.get('title', '')} {news_item.get('description', '')}"
        
        # NLP 分析
        nlp_result = self.nlp_analyzer.analyze_text(text_to_analyze)
        
        # 添加分析結果到新聞項目
        analyzed_item = news_item.copy()
        analyzed_item.update({
            'sentiment_score': nlp_result['sentiment']['score'],
            'sentiment_label': nlp_result['sentiment']['label'],
            'sentiment_confidence': nlp_result['sentiment']['confidence'],
            'event_types': nlp_result['event_types'],
            'mentioned_projects': nlp_result['mentioned_projects'],
            'hype_score': nlp_result['hype_score'],
            'analysis_completed': True
        })
        
        return analyzed_item
    
    def calculate_project_scores(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """計算項目投資分數"""
        # 預設值
        hype_score = project_data.get('hype_score', 0.0)
        tvl_growth = project_data.get('tvl_growth', 0.0)
        sentiment_score = project_data.get('sentiment_score', 0.0)
        unlock_risk = project_data.get('unlock_risk', 0.0)
        
        # 計算投資分數
        investment_score = (
            self.weights['hype_score'] * hype_score +
            self.weights['tvl_growth'] * tvl_growth +
            self.weights['sentiment_score'] * sentiment_score +
            self.weights['unlock_risk'] * unlock_risk
        )
        
        # 限制分數範圍
        investment_score = max(-1.0, min(1.0, investment_score))
        
        # 確定風險等級
        if investment_score > 0.5:
            risk_level = 'Low'
            recommendation = 'Strong Buy'
        elif investment_score > 0.2:
            risk_level = 'Medium'
            recommendation = 'Buy'
        elif investment_score > -0.2:
            risk_level = 'Medium'
            recommendation = 'Hold'
        elif investment_score > -0.5:
            risk_level = 'High'
            recommendation = 'Sell'
        else:
            risk_level = 'Very High'
            recommendation = 'Strong Sell'
        
        return {
            'project_name': project_data.get('project_name', 'Unknown'),
            'investment_score': investment_score,
            'hype_score': hype_score,
            'tvl_growth': tvl_growth,
            'sentiment_score': sentiment_score,
            'unlock_risk': unlock_risk,
            'risk_level': risk_level,
            'recommendation': recommendation,
            'predicted_return_7d': self._predict_return(investment_score)
        }
    
    def _predict_return(self, investment_score: float) -> float:
        """預測 7 日回報率"""
        # 簡單的線性預測模型
        # 實際應用中應該使用更複雜的機器學習模型
        base_return = investment_score * 0.3  # 基礎回報率
        
        # 添加一些隨機性和市場因素
        market_factor = 0.05  # 市場整體趨勢
        volatility = 0.1  # 波動性
        
        predicted_return = base_return + market_factor
        
        # 限制預測範圍
        return max(-0.5, min(0.5, predicted_return))
    
    def generate_market_summary(self, market_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """生成市場摘要"""
        summary = {
            'total_protocols': 0,
            'total_tvl': 0,
            'avg_sentiment': 0,
            'top_gainers': [],
            'top_losers': [],
            'trending_topics': []
        }
        
        try:
            # 處理 TVL 數據
            if 'tvl' in market_data:
                tvl_data = market_data['tvl']
                summary['total_protocols'] = len(tvl_data)
                summary['total_tvl'] = sum(item.get('tvl', 0) for item in tvl_data)
                
                # 找出漲幅最大和最小的協議
                sorted_by_change = sorted(tvl_data, key=lambda x: x.get('change_7d', 0), reverse=True)
                summary['top_gainers'] = sorted_by_change[:5]
                summary['top_losers'] = sorted_by_change[-5:]
            
            # 處理價格數據
            if 'prices' in market_data:
                price_data = market_data['prices']
                avg_change = sum(item.get('change_24h', 0) for item in price_data) / len(price_data) if price_data else 0
                summary['avg_price_change_24h'] = avg_change
            
            # 處理新聞數據
            if 'news' in market_data:
                news_data = market_data['news']
                if news_data:
                    sentiments = [item.get('sentiment_score', 0) for item in news_data if 'sentiment_score' in item]
                    summary['avg_sentiment'] = sum(sentiments) / len(sentiments) if sentiments else 0
                    
                    # 提取熱門話題
                    all_projects = []
                    for item in news_data:
                        all_projects.extend(item.get('mentioned_projects', []))
                    
                    from collections import Counter
                    project_counts = Counter(all_projects)
                    summary['trending_topics'] = project_counts.most_common(10)
        
        except Exception as e:
            logger.error(f"生成市場摘要時發生錯誤: {e}")
        
        return summary
