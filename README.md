# DeFi 自動化投研系統

一個全自動的 DeFi 項目投資研究和分析系統，整合數據收集、情緒分析、量化建模和自動推播功能。

## 🚀 功能特色

### 📊 數據收集
- **RSS 新聞收集**: 自動收集 CoinTelegraph、CoinDesk、The Block 等媒體新聞
- **Twitter 數據挖掘**: 監控重要 DeFi KOL 和項目官方帳號，實時捕捉市場情緒
- **API 數據整合**: 整合 CoinGecko、DefiLlama、CryptoPanic 等 API
- **實時價格監控**: 追蹤主要 DeFi 代幣價格和市值變化
- **TVL 數據分析**: 監控協議總鎖倉價值變化趨勢

### 🧠 智能分析
- **多源情緒分析**: 使用 NLP 技術分析新聞和 Twitter 情緒傾向
- **Twitter 專業分析**: 影響力評估、FOMO 指標、病毒傳播潛力分析
- **事件分類**: 自動識別上幣、投資、代幣解鎖等事件類型
- **項目識別**: 智能提取新聞和推文中提及的 DeFi 項目
- **炒作指數**: 計算市場炒作程度和熱度分數
- **技術信號提取**: 從推文中提取價格目標和技術分析信號

### 📈 量化建模
- **技術指標**: 計算移動平均線、RSI、波動率等技術指標
- **風險評估**: 評估價格波動風險、流動性風險、解鎖風險
- **投資評分**: 綜合多維度指標計算投資分數
- **收益預測**: 預測短期價格走勢和預期收益

### 🔔 自動推播
- **Telegram Bot**: 支持 Telegram 機器人推送
- **LINE Notify**: 支持 LINE 通知推送
- **智能提醒**: 重要新聞和投資機會自動提醒
- **定時報告**: 每日市場摘要和投資建議

### 🖥️ Web 介面
- **實時儀表板**: 展示市場概況和關鍵指標
- **新聞中心**: 最新 DeFi 新聞和情緒分析
- **Twitter 分析**: 熱門推文、情緒分布、趨勢話題
- **價格圖表**: 互動式價格趨勢圖表
- **TVL 排行**: 協議 TVL 排行榜和變化趨勢

## 🛠️ 技術架構

```
[數據來源收集] → [關鍵訊息萃取與標記] → [量化分析模型] → [風險/收益預測] → [優先排序與推送]
```

### 核心組件
- **數據收集器**: RSS、Twitter API、第三方 API、Web Scraping
- **NLP 分析器**: 多源情緒分析、事件提取、項目識別
- **Twitter 分析器**: 影響力評估、FOMO 檢測、病毒潜力分析
- **量化模型**: 技術指標、風險評估、預測模型
- **資料庫**: SQLite 數據存儲和查詢
- **Web 應用**: Flask + Bootstrap + Plotly
- **排程系統**: APScheduler 自動化任務
- **通知系統**: Telegram Bot + LINE Notify

## 📦 安裝與使用

### 環境要求
- Python 3.8+
- Poetry (推薦) 或 pip

### 安裝步驟

1. **克隆項目**
```bash
git clone <repository-url>
cd defiapp
```

2. **安裝依賴**
```bash
# 使用 Poetry (推薦)
poetry install

# 或使用 pip
pip install -r requirements.txt
```

3. **配置環境變數** (可選)
```bash
# Twitter API 配置 (強烈推薦)
export TWITTER_BEARER_TOKEN="your_twitter_bearer_token"

# Telegram Bot 配置
export TELEGRAM_BOT_TOKEN="your_bot_token"
export TELEGRAM_CHAT_ID="your_chat_id"

# LINE Notify 配置
export LINE_NOTIFY_TOKEN="your_line_token"
```

### 運行系統

1. **完整啟動**
```bash
poetry run python run.py run
```

2. **僅收集數據**
```bash
poetry run python run.py collect
```

3. **僅分析數據**
```bash
poetry run python run.py analyze
```

4. **檢查系統狀態**
```bash
poetry run python run.py status
```

5. **運行測試**
```bash
poetry run python run.py test
```

### Web 介面訪問
系統啟動後，訪問 http://localhost:5000 查看 Web 介面。

## 📋 使用說明

### 數據收集
系統會自動每小時收集一次數據，包括：
- RSS 新聞源的最新文章
- 主要 DeFi 代幣的價格數據
- 協議的 TVL 數據

### 分析功能
- 每2小時自動分析新收集的新聞
- 計算情緒分數和事件分類
- 生成投資評分和風險評估

### 推播通知
- 每天早上8點發送市場摘要
- 每天晚上8點發送投資建議
- 重要新聞實時推送
- 每15分鐘檢查重要事件

## 🔧 配置說明

### 監控關鍵字
在 `defiapp/config.py` 中可以自定義監控的關鍵字：
```python
MONITORED_KEYWORDS = [
    "DeFi", "yield farming", "liquidity mining",
    "staking", "DEX", "AMM", "lending"
]
```

### 投資評分權重
可以調整投資評分的權重配置：
```python
INVESTMENT_SCORE_WEIGHTS = {
    "hype_score": 0.3,      # 炒作分數權重
    "tvl_growth": 0.2,      # TVL 增長權重
    "sentiment_score": 0.2,  # 情緒分數權重
    "unlock_risk": -0.3     # 解鎖風險權重 (負值)
}
```

### RSS 新聞源
可以添加或修改 RSS 新聞源：
```python
RSS_FEEDS = [
    "https://cointelegraph.com/rss",
    "https://www.coindesk.com/arc/outboundfeeds/rss/",
    "https://theblock.co/rss.xml"
]
```

## 📊 數據結構

### 新聞數據表
```sql
CREATE TABLE news (
    id INTEGER PRIMARY KEY,
    title TEXT NOT NULL,
    link TEXT UNIQUE,
    description TEXT,
    published_at DATETIME,
    sentiment_score REAL,
    news_type TEXT,
    tokens_mentioned TEXT
);
```

### 價格數據表
```sql
CREATE TABLE price_data (
    id INTEGER PRIMARY KEY,
    coin_id TEXT NOT NULL,
    price_usd REAL,
    market_cap REAL,
    volume_24h REAL,
    change_24h REAL
);
```

### TVL 數據表
```sql
CREATE TABLE tvl_data (
    id INTEGER PRIMARY KEY,
    protocol_name TEXT NOT NULL,
    tvl REAL,
    change_1d REAL,
    change_7d REAL,
    category TEXT
);
```

## 🧪 測試

運行完整測試套件：
```bash
poetry run python run.py test
```

運行特定測試：
```bash
poetry run python -m pytest tests/test_data_collector.py -v
poetry run python -m pytest tests/test_analyzer.py -v
```

## 📈 性能優化

### 數據庫優化
- 使用索引加速查詢
- 定期清理舊數據
- 批量插入提高效率

### API 請求優化
- 實現請求限流
- 使用連接池
- 錯誤重試機制

### 內存管理
- 及時釋放大型數據對象
- 使用生成器處理大量數據
- 監控內存使用情況

## 🚨 注意事項

1. **API 限制**: 注意各 API 的請求限制，避免被封禁
2. **數據準確性**: 數據僅供參考，投資決策需謹慎
3. **風險提示**: 加密貨幣投資存在高風險
4. **合規使用**: 確保符合當地法律法規

## 🤝 貢獻指南

歡迎提交 Issue 和 Pull Request！

1. Fork 項目
2. 創建功能分支
3. 提交更改
4. 推送到分支
5. 創建 Pull Request

## 📄 許可證

MIT License

## 📞 聯繫方式

如有問題或建議，請通過以下方式聯繫：
- Email: <EMAIL>
- GitHub Issues: [項目 Issues 頁面]

---

⚠️ **免責聲明**: 本系統僅供學習和研究使用，不構成投資建議。加密貨幣投資存在高風險，請謹慎決策。